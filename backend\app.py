import json
import time
import os
from contextlib import asynccontextmanager
from datetime import datetime
from functools import lru_cache
from typing import Optional, Generic, TypeVar, Any, List

from fastapi import FastAP<PERSON>, HTTPException, status, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import FileResponse
from fastapi.staticfiles import StaticFiles
from passlib.context import CryptContext
from pydantic import BaseModel, EmailStr, Field

import utils as U
from server.gate.creator_info_server import CreatorInfoServer
from backend.query.query_board_data import (
    collect_all_dahanghai_and_follower_rise_num_stuff,
    collect_all_live_status_stuff,
    collect_target_flow_with_target_vtuber,
    collect_target_flow_with_target_vtuber_by_period,
    collect_whole_flow_from_all_vtubers,
    collect_whole_info_from_all_vtubers,
)
from backend.query.query_live_info import (
    query_live_info_with_room_id,
    query_minutes_live_info_with_live_id,
    query_now_live_info_by_room,
    query_whole_live_info_with_live_id,
)
from backend.query.query_user_data import (
    calculate_dahanghai_rate_by_mid,
    calculate_follower_rate_by_mid,
    query_all_dynamics_comments_by_mid,
    query_all_video_comments_by_mid,
    query_all_video_list_by_mid,
    query_comment_wordcloud,
    query_current_dahanghai_change_num,
    query_current_dynamics,
    query_current_follower_change_num,
    query_current_stat_by_mid,
    query_current_video_day_views,
    query_current_videos,
    query_dahanghai_list_by_uid_and_datetime,
    query_fans_medal_rank_by_datetime,
    query_followers_list,
    query_followers_review_list,
    query_followers_review_rate,
    query_latest_fans_medal_rank,
    query_now_user_dahanghai_num_by_mid,
    query_now_user_follower_num_by_mid,
    query_peroid_user_all_stat_by_uid_and_time,
    query_recent_comments_sentiment_value,
    query_recent_info,
    query_recent_relationships,
    query_rise_reason_from_ai_gen_table_by_date,
    query_single_video_rencent_day_data,
    query_single_video_target_day_data,
    query_tieba_summaries_from_ai_gen_table_by_date,
    query_tieba_threads,
    query_tieba_whole,
    query_top_n_comments,
    query_top_n_comments_user,
    query_top_n_dynamics,
    query_top_n_videos,
    query_top_n_view_rise_day_data_period,
    query_top_n_view_rise_target_day_data,
    query_user_dynamics_by_mid,
    query_user_info_by_mid,
    query_video_ai_conclusion_by_bvid,
    query_whole_dahanghai_num_by_mid_and_recent,
    query_whole_user_all_stat_by_uid_and_recent,
    query_whole_user_follower_num_by_mid_and_recent,
)
from backend.query.query_creator_data import (
    query_overview_stat_by_date_range,
    query_overview_stat_by_date,
    query_attention_analyze_by_date_range,
    query_attention_analyze_by_date,
    query_archive_analyze_by_date_range,
    query_archive_analyze_by_date,
    query_fan_graph_by_date_range,
    query_fan_graph_by_date,
    query_fan_overview_by_date_range,
    query_fan_overview_by_date,
    query_video_compare_by_date_range,
    query_video_compare_by_date,
    query_video_pandect_by_date_range,
    query_video_pandect_by_date,
    query_video_survey_by_date_range,
    query_video_survey_by_date,
    query_video_source_by_date_range,
    query_video_source_by_date,
    query_video_view_data_by_date_range,
    query_video_view_data_by_date,
)
from backend.query.query_web_user_info import (
    check_web_user_info_table,
    delete_web_user_info,
    get_web_user_by_email,
    login_web_user_info,
    modify_web_user_password,
    register_web_user_info,
)
from sql.db_pool import initialize_pool, shutdown_pool
from logger import logger

pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Generic type for response data
T = TypeVar('T')

class ApiResponse(BaseModel, Generic[T]):
    """Standard API response format"""
    code: int = Field(description="Response code")
    message: str = Field(description="Response message")
    data: Optional[T] = Field(default=None, description="Response data")

class ErrorDetail(BaseModel):
    """Error detail information"""
    field: Optional[str] = Field(default=None, description="Error field")
    message: str = Field(description="Error message")
    code: Optional[str] = Field(default=None, description="Error code")

class ApiError(BaseModel):
    """Standard API error response format"""
    code: int = Field(description="Error code")
    message: str = Field(description="Error message")
    details: Optional[List[ErrorDetail]] = Field(default=None, description="Error details")

class PaginatedResponse(BaseModel, Generic[T]):
    """Paginated response format"""
    data: List[T] = Field(description="Response data list")
    total: int = Field(description="Total count")
    page: int = Field(description="Current page")
    page_size: int = Field(description="Page size")
    has_next: bool = Field(description="Has next page")

class WebUserBase(BaseModel):
    email: EmailStr


class WebUserCreate(WebUserBase):
    username: str
    password: str
    phone: Optional[str] = None


class WebUserLogin(BaseModel):
    email: EmailStr
    password: str


class WebUserPasswordUpdate(WebUserBase):
    new_password: str


class WebUserDelete(WebUserBase):
    pass


class Token(BaseModel):
    access_token: str
    token_type: str


class UserInDB(WebUserBase):
    username: str
    password_hash: str
    phone: Optional[str] = None


def get_password_hash(password):  # Still used for registration in this file
    return pwd_context.hash(password)


def create_success_response(data: Any = None, message: str = "Success", code: int = 200) -> dict:
    """Create a standard success response"""
    return {
        "code": code,
        "message": message,
        "data": data
    }

def create_error_response(message: str, code: int = 400, details: Optional[List[dict]] = None) -> dict:
    """Create a standard error response"""
    response = {
        "code": code,
        "message": message
    }
    if details:
        response["details"] = details
    return response

def create_paginated_response(data: List[Any], total: int, page: int = 1, page_size: int = 20) -> dict:
    """Create a paginated response"""
    has_next = (page * page_size) < total
    return {
        "data": data,
        "total": total,
        "page": page,
        "page_size": page_size,
        "has_next": has_next
    }


@asynccontextmanager
async def lifespan(app: FastAPI):
    # Application startup
    logger.info("Application startup: Initializing database pool.")
    await initialize_pool()
    logger.info("Database pool initialized.")

    logger.info(
        "Application startup: Checking and creating web_user_info table if not exists."
    )
    await (
        check_web_user_info_table()
    )
    logger.info("Web_user_info table check complete.")

    logger.info("Application startup: Initializing CreatorInfoServer.")
    app.state.creator_info_server = CreatorInfoServer()
    await app.state.creator_info_server.initialize_async()
    logger.info("CreatorInfoServer initialized.")

    yield

    # Application shutdown
    logger.info("Application shutdown: Closing database pool.")
    await shutdown_pool()
    logger.info("Database pool closed.")


app = FastAPI(lifespan=lifespan)

# Mount static files for word cloud images
app.mount("/wordcloud", StaticFiles(directory="frontend/public/wordcloud"), name="wordcloud")

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Performance monitoring middleware
@app.middleware("http")
async def performance_monitoring_middleware(request: Request, call_next):
    """Monitor API endpoint performance and log slow requests"""
    start_time = time.time()

    # Process the request
    response = await call_next(request)

    # Calculate processing time
    process_time = time.time() - start_time

    # Add performance header
    response.headers["X-Process-Time"] = f"{process_time:.3f}"

    # Log slow requests (> 1 second)
    if process_time > 1.0:
        logger.warning(f"Slow request: {request.method} {request.url.path} took {process_time:.3f}s")
    elif process_time > 0.5:
        logger.info(f"Medium request: {request.method} {request.url.path} took {process_time:.3f}s")
    else:
        logger.debug(f"Fast request: {request.method} {request.url.path} took {process_time:.3f}s")

    return response


@lru_cache(maxsize=None)
def get_user_mid(char):
    """
    char maybe fullname / uid / shortNmae / enName
    """
    vups = U.read_vups_config()
    for vup in vups:
        if vup["uid"] == char:
            return char
    uid = U.get_vup_uid_by_any_name(char)
    if uid is not None:
        return str(uid)

    char_zh = U.get_zh_role_name(char)
    uid = U.get_vup_uid_by_short_name(char_zh)
    if uid is None:
        raise Exception(f"Character {str(char)} not found in vups.json")
    return str(uid)


@lru_cache(maxsize=None)
def get_user_room_id(char):
    """
    char maybe fullname / uid / shortNmae / enName
    """
    vups = U.read_vups_config()
    for vup in vups:
        if vup["uid"] == char:
            return vup["roomId"]
    char_zh = U.get_zh_role_name(char)
    room_id = U.get_vup_room_id_by_short_name(char_zh)
    if room_id is None:
        logger.warning(f"Character {str(char)} not found in vups.json")
        return None
    return str(room_id)


def get_creator_info_server():
    """获取 CreatorInfoServer 实例"""
    return app.state.creator_info_server


@app.get("/")
async def read_root():
    return {"It's a demo web for info collection."}


# --- Web User Authentication API Endpoints ---


@app.post("/web/users", status_code=status.HTTP_201_CREATED)
async def create_user(user_in: WebUserCreate):
    """
    Create a new web user.
    - **email**: User's email (must be unique).
    - **username**: User's chosen username.
    - **password**: User's password.
    - **phone**: User's phone number (optional).
    """
    logger.info(f"Attempting to create user with email: {user_in.email}")
    password_hash = get_password_hash(user_in.password)
    try:
        user_id = await register_web_user_info(
            username=user_in.username,
            password_hash=password_hash,
            email=user_in.email,
            phone=user_in.phone,
        )
        logger.info(
            f"User {user_in.username} created successfully with ID: {user_id}"
        )

        user_data = {
            "id": user_id,
            "email": user_in.email,
            "username": user_in.username,
            "phone": user_in.phone,
            "created_at": datetime.now().isoformat()
        }

        return create_success_response(
            data=user_data,
            message="User created successfully",
            code=201
        )
    except ValueError as e:
        logger.error(f"User creation failed for email {user_in.email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=create_error_response(str(e), 400)
        )
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during user creation for email {user_in.email}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Internal server error during user creation", 500)
        )

@app.post("/web/auth/sessions")
async def create_auth_session(form_data: WebUserLogin):
    """
    Create authentication session and return a token.
    - **email**: User's email.
    - **password**: User's password.
    """
    logger.info(f"Authentication attempt for email: {form_data.email}")
    user_data_dict = await login_web_user_info(
        email=form_data.email, password=form_data.password
    )

    if not user_data_dict:
        logger.warning(
            f"Authentication failed for email {form_data.email}: Incorrect email or password."
        )
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=create_error_response("Incorrect email or password", 401)
        )

    user_id = user_data_dict.get("user_id")
    username = user_data_dict.get("username")

    # For simplicity, returning a dummy token. Real implementation would use JWT.
    access_token = f"fake-jwt-token-for-{username}-{user_id}"

    token_data = {
        "access_token": access_token,
        "token_type": "bearer",
        "user": {
            "id": user_id,
            "username": username,
            "email": form_data.email
        }
    }

    logger.info(f"User {username} (ID: {user_id}) authenticated successfully.")
    return create_success_response(
        data=token_data,
        message="Authentication successful"
    )

@app.patch("/web/users/password", status_code=status.HTTP_200_OK)
async def update_user_password(update_data: WebUserPasswordUpdate):
    """
    Update web user's password.
    - **email**: User's email.
    - **new_password**: User's new password.
    """
    logger.info(f"Attempting to update password for email: {update_data.email}")

    user_data_dict = await get_web_user_by_email(email=update_data.email)
    if not user_data_dict:
        logger.warning(
            f"Password update failed: User not found for email {update_data.email}"
        )
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=create_error_response("User not found", 404)
        )

    new_hashed_password = get_password_hash(update_data.new_password)
    try:
        success = await modify_web_user_password(
            email=update_data.email,
            new_password_hash=new_hashed_password,
        )
        if success:
            logger.info(f"Password updated successfully for email: {update_data.email}")
            return create_success_response(
                message="Password updated successfully"
            )
        else:
            logger.error(
                f"Password update failed for email {update_data.email}, modify_web_user_password returned false."
            )
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=create_error_response("Failed to update password", 500)
            )
    except ValueError as e:
        logger.error(f"Password update failed for email {update_data.email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=create_error_response(str(e), 404)
        )
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during password update for {update_data.email}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Internal server error during password update", 500)
        )

@app.delete("/web/users", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(user_del: WebUserDelete):
    """
    Delete a web user.
    - **email**: User's email.
    """
    logger.info(f"Attempting to delete user with email: {user_del.email}")

    try:
        success = await delete_web_user_info(email=user_del.email)
        if success:
            logger.info(f"User with email {user_del.email} deleted successfully.")
            # Return 204 No Content for successful deletion
            return None
        else:
            logger.warning(
                f"Deletion failed for email {user_del.email}: User not found or DB error."
            )
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response("User not found or could not be deleted", 404)
            )
    except ValueError as e:
        logger.error(f"Deletion failed for email {user_del.email}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=create_error_response(str(e), 404)
        )
    except Exception as e:
        logger.error(
            f"An unexpected error occurred during deletion for email {user_del.email}: {e}"
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Internal server error during deletion", 500)
        )

@app.get("/basic/vups")
async def read_basic_vups():
    """
    Returns:
        Standard API response with Vup UID

    Example:
        GET /basic/vups?vtuber=星瞳
    """
    try:
        return create_success_response(
            data=U.read_vups_config(),
            message="Vup List retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get Vup list: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=create_error_response(f"Vup list not found", 404)
        )

# TODO: func: Add to vups.json

@app.get("/basic/mid")
async def read_basic_mid(vtuber_name: Optional[str] = "星瞳"):
    """
    Args:
        vtuber_name: Vup name

    Returns:
        Standard API response with Vup UID

    Example:
        GET /basic/mid?vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Vup UID retrieved successfully",
            "data": {
                "result": "401315430",
                "vtuber_name": "星瞳"
            }
        }
    """
    try:
        mid = get_user_mid(vtuber_name)
        return create_success_response(
            data={"mid": mid, "vtuber_name": vtuber_name},
            message="Vup UID retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get UID for Vup {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=create_error_response(f"Vup {vtuber_name} not found", 404)
        )

@app.get("/basic/follower/current")
async def read_basic_current_follower(vtuber_name: Optional[str] = "星瞳"):
    """
    Get Vup's current follower count using query parameter.

    Args:
        vtuber_name: Vup name (passed as query parameter to avoid URL encoding issues with Chinese characters)

    Returns:
        Standard API response with current follower count

    Example:
        GET /vtubers/followers/current?vtuber_name=星瞳

        Response:
        {
            "code": 200,
            "message": "Current follower count retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "result": 1024459,
                "timestamp": "2025-08-11T15:09:01.648869"
            }
        }
    """
    try:
        mid = get_user_mid(vtuber_name)
        follower_count = await query_now_user_follower_num_by_mid(mid)
        return create_success_response(
            data={
                "vtuber_name": vtuber_name,
                "mid": mid,
                "result": follower_count,
                "timestamp": datetime.now().isoformat()
            },
            message="Current follower count retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get current followers for Vup {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve follower count", 500)
        )

@app.get("/basic/follower/all")
async def read_basic_all_follower(vtuber_name: Optional[str] = "星瞳", recent: int = -1, limit: int = 1000, offset: int = 0):
    """
    Use GET /vtubers/followers/history?vtuber_name={name} instead to avoid URL encoding issues.

    Args:
        vtuber_name: Vup name
        recent: Recent time period (-1 for all)
        limit: Maximum number of records to return
        offset: Number of records to skip

    Returns:
        Standard API response with follower history data

    Example:
        GET /basic/follower/all?vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Follower history retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "list": [
                [
                    "2025081016",
                    1024080
                ]
                ],
                "pagination": {
                "total": 24,
                "limit": 1,
                "offset": 0,
                "has_more": true
                }
            }
        }
    """
    try:
        mid = get_user_mid(vtuber_name)
        history_data = await query_whole_user_follower_num_by_mid_and_recent(mid, recent)

        total = len(history_data) if history_data else 0
        paginated_data = history_data[offset:offset + limit] if history_data else [] # TODO: real offset

        return create_success_response(
            data={
                "vtuber_name": vtuber_name,
                "mid": mid,
                "list": paginated_data,
                "pagination": {
                    "total": total,
                    "limit": limit,
                    "offset": offset,
                    "has_more": (offset + limit) < total
                }
            },
            message="Follower history retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get follower history for Vup {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve follower history", 500)
        )

@app.get("/basic/dahanghai/current")
async def read_basic_current_dahanghai(vtuber_name: Optional[str] = "星瞳"):
    """
    Get Vup's current dahanghai (guard) count using query parameter.

    Args:
        vtuber_name: Vup name (passed as query parameter to avoid URL encoding issues with Chinese characters)

    Returns:
        Standard API response with current dahanghai count

    Example:
        GET /vtubers/dahanghai/current?vtuber_name=星瞳

        Response:
        {
            "code": 200,
            "message": "Current dahanghai count retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "result": 1817,
                "timestamp": "2025-08-11T15:13:20.377213"
            }
        }
    """
    try:
        mid = get_user_mid(vtuber_name)
        dahanghai_count = await query_now_user_dahanghai_num_by_mid(mid)
        return create_success_response(
            data={
                "vtuber_name": vtuber_name,
                "mid": mid,
                "result": dahanghai_count,
                "timestamp": datetime.now().isoformat()
            },
            message="Current dahanghai count retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get current dahanghai for Vup {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve dahanghai count", 500)
        )

@app.get("/basic/dahanghai/all")
async def read_basic_all_dahanghai(vtuber_name: Optional[str] = "星瞳", recent: int = -1, limit: int = 1000, offset: int = 0):
    """
    Args:
        vtuber_name: Vup name
        recent: Recent time period (-1 for all)
        limit: Maximum number of records to return
        offset: Number of records to skip

    Returns:
        Standard API response with dahanghai history data

        Response:
        {
            "code": 200,
            "message": "Dahanghai history retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "list": [
                    [
                        "2025081016",
                        1809
                    ]
                ],
                "pagination": {
                    "total": 24,
                    "limit": 1,
                    "offset": 0,
                    "has_more": true
                }
            }
        }
    """
    try:
        mid = get_user_mid(vtuber_name)
        history_data = await query_whole_dahanghai_num_by_mid_and_recent(mid, recent)

        # Apply pagination
        total = len(history_data) if history_data else 0
        paginated_data = history_data[offset:offset + limit] if history_data else []

        return create_success_response(
            data={
                "vtuber_name": vtuber_name,
                "mid": mid,
                "list": paginated_data,
                "pagination": {
                    "total": total,
                    "limit": limit,
                    "offset": offset,
                    "has_more": (offset + limit) < total
                }
            },
            message="Dahanghai history retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get dahanghai history for Vup {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve dahanghai history", 500)
        )

@app.get("/basic/dahanghai/rate")
async def read_basic_dahanghai_rate(vtuber: Optional[str] = "星瞳", recent: int = 90):
    """
    Get Vup's dahanghai (guard) growth rate.

    Args:
        vtuber: Vup name (default: "星瞳")
        recent: Time window in days for rate calculation (default: 90, -1 for all available data)

    Returns:
        Standard API response with dahanghai growth rate

    Example:
        GET /basic/dahanghai/rate?vtuber=星瞳&recent=90

        Response:
        {
            "code": 200,
            "message": "Dahanghai growth rate retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "result": "10.5%",
                "period_days": 90,
                "timestamp": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent < -1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be -1 or a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Calculate growth rate
        growth_rate = await calculate_dahanghai_rate_by_mid(mid, recent)

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "result": growth_rate,
                "period_days": recent,
                "timestamp": datetime.now().isoformat()
            },
            message="Dahanghai growth rate retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get dahanghai growth rate for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve dahanghai growth rate", 500)
        )


@app.get("/basic/follower/rate")
async def read_basic_follower_rate(vtuber: Optional[str] = "星瞳", recent: int = 90):
    """
    Get Vup's follower growth rate.

    Args:
        vtuber: Vup name (default: "星瞳")
        recent: Time window in days for rate calculation (default: 90, -1 for all available data)

    Returns:
        Standard API response with follower growth rate

    Example:
        GET /basic/follower/rate?vtuber=星瞳&recent=90

        Response:
        {
            "code": 200,
            "message": "Follower growth rate retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "growth_rate": "-0.7%",
                "period_days": 90,
                "timestamp": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent < -1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be -1 or a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Calculate growth rate
        growth_rate = await calculate_follower_rate_by_mid(mid, recent)

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "growth_rate": growth_rate,
                "period_days": recent,
                "timestamp": datetime.now().isoformat()
            },
            message="Follower growth rate retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get follower growth rate for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve follower growth rate", 500)
        )


@app.get("/basic/stat/current")
async def read_basic_current_stat(vtuber: Optional[str] = "星瞳"):
    """
    Get Vup's current basic statistics.

    Args:
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with current statistics

    Example:
        GET /basic/stat/current?vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Current statistics retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "result": {
                    "uid": "401315430",
                    "timestamp": 1747710000,
                    "datetime": "2025-05-20T11:00:00",
                    "follower_num": 974497,
                    "dahanghai_num": 269,
                    "video_total_num": 99663571,
                    "article_total_num": 13455,
                    "likes_total_num": 11801146,
                    "elec_num": 1460
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query current statistics
        stat_record = await query_current_stat_by_mid(mid)

        if stat_record is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No statistics found for Vup '{vtuber}'", 404)
            )

        # Convert record to dict and format datetime
        stat_dict = dict(stat_record)
        if 'datetime' in stat_dict and stat_dict['datetime']:
            stat_dict['datetime'] = stat_dict['datetime'].isoformat()

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "result": stat_dict,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Current statistics retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get current statistics for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve current statistics", 500)
        )


@app.get("/basic/stats/period")
async def get_vtuber_stats_by_period_query(vtuber_name: str, start_date: str, end_date: str):
    """
    Get Vup's statistics for a specific period using query parameter.

    Args:
        vtuber_name: Vup name (passed as query parameter to avoid URL encoding issues with Chinese characters)
        start_date: Start date (YYYY-MM-DD format)
        end_date: End date (YYYY-MM-DD format)

    Returns:
        Standard API response with period statistics

    Example:
        GET /basic/stats/period?vtuber_name=星瞳&start_date=2024-01-01&end_date=2024-01-31
    """
    try:
        mid = get_user_mid(vtuber_name)
        stats_data = await query_peroid_user_all_stat_by_uid_and_time(mid, start_date, end_date)

        return create_success_response(
            data={
                "vtuber_name": vtuber_name,
                "mid": mid,
                "result": stats_data
            },
            message="Period statistics retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get period stats for Vup {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve period statistics", 500)
        )

@app.get("/basic/stat/all")
async def read_basic_all_stat(vtuber: Optional[str] = "星瞳", recent: int = -1, limit: Optional[int] = None):
    """
    Get Vup's historical statistics data.

    Args:
        vtuber: Vup name (default: "星瞳")
        recent: Number of recent days to retrieve (-1 for all available data, limited to 1000 records by default)
        limit: Maximum number of records to return (optional, defaults to 1000 when recent=-1)

    Returns:
        Standard API response with historical statistics

    Example:
        GET /basic/stat/all?vtuber=星瞳&recent=30
        GET /basic/stat/all?vtuber=星瞳&recent=-1&limit=500

        Response:
        {
            "code": 200,
            "message": "Historical statistics retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "period_days": 30,
                "list": [
                    {
                        "datetime": "2025051415",
                        "video_total_num": 99391051,
                        "article_total_num": 13427,
                        "likes_total_num": 11765417,
                        "elec_num": 1456,
                        "follower_num": 972639,
                        "dahanghai_num": 276
                    },
                    ...
                ],
                "total_records": 720,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent < -1:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be -1 or a positive integer", 400)
            )

        if limit is not None and limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'limit' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query historical statistics with optimized parameters
        import time
        start_time = time.time()

        raw_stats = await query_whole_user_all_stat_by_uid_and_recent(mid, recent, limit)

        query_time = time.time() - start_time
        logger.info(f"Query completed for {vtuber} (recent={recent}, limit={limit}) in {query_time:.3f}s, returned {len(raw_stats)} records")

        # Optimized data conversion using list comprehension
        structured_stats = [
            {
                "datetime": stat_row[0],
                "video_total_num": stat_row[1],
                "article_total_num": stat_row[2],
                "likes_total_num": stat_row[3],
                "elec_num": stat_row[4],
                "follower_num": stat_row[5],
                "dahanghai_num": stat_row[6]
            }
            for stat_row in raw_stats if len(stat_row) >= 7
        ]

        # Add performance and limit information to response
        response_data = {
            "vtuber_name": vtuber,
            "mid": mid,
            "period_days": recent,
            "list": structured_stats,
            "total_records": len(structured_stats),
            "retrieved_at": datetime.now().isoformat(),
            "query_time_seconds": round(query_time, 3)
        }

        # Add limit info if applied
        if recent == -1:
            response_data["limit_applied"] = limit or 1000
            response_data["note"] = "Results limited to prevent timeouts. Use 'limit' parameter to adjust."

        return create_success_response(
            data=response_data,
            message="Historical statistics retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get historical statistics for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve historical statistics", 500)
        )


@app.get("/basic/medal_rank")
async def read_basic_medal_rank(vtuber: Optional[str] = "星瞳"):
    """
    Get Vup's latest fans medal ranking (top 10).

    Args:
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with fans medal ranking

    Example:
        GET /basic/medal_rank?vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Fans medal ranking retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "list": {
                    "uid": "401315430",
                    "name": "星瞳",
                    "liveid": "22886883",
                    "datetime": "2025-05-14T00:00:00",
                    "rank_list": [
                        {
                            "uid": 486553605,
                            "face": "https://i0.hdslb.com/bfs/face/9d9a8d9e3b8926ff1aa8adf9363e9a87c2171181.jpg",
                            "rank": 1,
                            "color": 7996451,
                            "level": 34,
                            "uname": "钉钩鱼骑不动",
                            "isSelf": 0,
                            "special": "",
                            "target_id": 401315430,
                            "is_lighted": 1,
                            "medal_name": "瞳星结",
                            "guard_level": 2,
                            "medal_color_end": 15304379,
                            "medal_color_start": 7996451,
                            "medal_color_border": 16771156
                        }
                    ]
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query fans medal ranking
        ranking_data = await query_latest_fans_medal_rank(mid)

        if ranking_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No fans medal ranking found for Vup '{vtuber}'", 404)
            )

        # Format datetime if present
        if 'datetime' in ranking_data and ranking_data['datetime']:
            ranking_data['datetime'] = ranking_data['datetime'].isoformat()

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "list": ranking_data,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Fans medal ranking retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get fans medal ranking for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve fans medal ranking", 500)
        )

@app.get("/basic/medal_rank/target")
async def read_basic_medal_rank_target(
    target_datetime: str, vtuber: Optional[str] = "星瞳"
):
    """
    Get Vup's fans medal ranking for a specific date.

    Args:
        target_datetime: Target date in YYYY-MM-DD format
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with fans medal ranking for the specified date

    Example:
        GET /basic/medal_rank/target?target_datetime=2025-05-14&vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Fans medal ranking for target date retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "target_date": "2025-05-14",
                "ranking": {
                    "uid": "401315430",
                    "name": "星瞳",
                    "liveid": "22886883",
                    "datetime": "2025-05-14T00:00:00",
                    "list": [
                        {
                            "uid": 486553605,
                            "face": "https://i0.hdslb.com/bfs/face/9d9a8d9e3b8926ff1aa8adf9363e9a87c2171181.jpg",
                            "rank": 1,
                            "color": 7996451,
                            "level": 34,
                            "uname": "钉钩鱼骑不动",
                            "isSelf": 0,
                            "special": "",
                            "target_id": 401315430,
                            "is_lighted": 1,
                            "medal_name": "瞳星结",
                            "guard_level": 2,
                            "medal_color_end": 15304379,
                            "medal_color_start": 7996451,
                            "medal_color_border": 16771156
                        }
                    ]
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not target_datetime or not target_datetime.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'target_datetime' cannot be empty", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Validate date format
        try:
            datetime.strptime(target_datetime, "%Y-%m-%d")
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query fans medal ranking for target date
        ranking_data = await query_fans_medal_rank_by_datetime(mid, target_datetime)

        if ranking_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No fans medal ranking found for Vup '{vtuber}' on date '{target_datetime}'", 404)
            )

        # Convert record to dict and format datetime
        ranking_dict = dict(ranking_data)
        if 'datetime' in ranking_dict and ranking_dict['datetime']:
            ranking_dict['datetime'] = ranking_dict['datetime'].isoformat()

        # Parse rank_list if it's a JSON string
        if 'rank_list' in ranking_dict and isinstance(ranking_dict['rank_list'], str):
            try:
                import json
                ranking_dict['rank_list'] = json.loads(ranking_dict['rank_list'])
            except json.JSONDecodeError:
                ranking_dict['rank_list'] = []

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "target_date": target_datetime,
                "result": ranking_dict,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Fans medal ranking for target date retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get fans medal ranking for Vup {vtuber} on date {target_datetime}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve fans medal ranking for target date", 500)
        )

@app.get("/basic/info")
async def read_basic_info(vtuber: Optional[str] = "星瞳"):
    """
    Get Vup's basic information including name, avatar, signature, birthday, and profile images.

    Args:
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with Vup's basic information

    Example:
        GET /basic/info?vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Vup basic information retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "result": {
                    "uid": "401315430",
                    "name": "星瞳_Official",
                    "face": "https://i0.hdslb.com/bfs/face/35ce8ca063124d4dda5c211039d0eb67eae3c797.jpg",
                    "sign": "时尚虚拟偶像 炫舞系列虚拟代言人 合作联系***********************",
                    "birthday": "10-21",
                    "top_photo": "http://i1.hdslb.com/bfs/space/cb1c3ef50e22b6096fde67febe863494caefebad.png",
                    "room_id": "22886883",
                    "live_url": "https://live.bilibili.com/22886883?broadcast_type=0&is_room_feed=1"
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query Vup basic information
        user_info = await query_user_info_by_mid(mid)

        if user_info is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No basic information found for Vup '{vtuber}'", 404)
            )

        # Convert record to dict
        profile_dict = dict(user_info)

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "result": profile_dict,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Vup basic information retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get basic information for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve Vup basic information", 500)
        )

@app.get("/live/info")
async def read_live_info(vtuber: Optional[str] = "星瞳"):
    """
    Get Vup's current live streaming information including status, title, cover, and room details.

    Args:
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with live streaming information

    Example:
        GET /live/info?vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Live streaming information retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "room_id": "22886883",
                "result": {
                    "live_status": 1,
                    "live_title": "【3D】下一个更乖",
                    "live_cover": "https://i0.hdslb.com/bfs/live/new_room_cover/8ab848423b88f25c7df6f02a72e1296e1d839032.jpg",
                    "parent_area": "虚拟主播",
                    "area": "虚拟日常",
                    "timestamp": 1747113480,
                    "datetime": "2025-05-13T13:18:00",
                    "live_action": "开始直播"
                },
                "status_description": {
                    "0": "下播",
                    "1": "开播",
                    "2": "轮播"
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup room ID
        try:
            room_id = get_user_room_id(vtuber)
            if not room_id:
                raise Exception("Room ID not found")
        except Exception as e:
            logger.error(f"Room ID for Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Room ID for Vup '{vtuber}' not found", 404)
            )

        # Query current live information
        live_info = await query_now_live_info_by_room(room_id)

        if live_info is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No live information found for Vup '{vtuber}'", 404)
            )

        # Convert record to dict and format datetime
        live_dict = dict(live_info)
        if 'datetime' in live_dict and live_dict['datetime']:
            live_dict['datetime'] = live_dict['datetime'].isoformat()

        # Add status description for better understanding
        status_descriptions = {
            0: "下播",
            1: "开播",
            2: "轮播"
        }

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "room_id": room_id,
                "result": live_dict,
                "status_description": status_descriptions,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Live streaming information retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get live information for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve live streaming information", 500)
        )

@app.get("/dynamics/list")
async def get_vtuber_dynamics_by_query(
    vtuber_name: Optional[str] = "星瞳",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 10,
    sort: str = "popularity",
    offset: int = 0
):
    """
    Get Vup's dynamics with filtering and sorting options using query parameter.

    Args:
        vtuber_name: Vup name (passed as query parameter to avoid URL encoding issues with Chinese characters)
        start_date: Start date filter (YYYY-MM-DD format)
        end_date: End date filter (YYYY-MM-DD format)
        limit: Maximum number of dynamics to return
        sort: Sort order ('popularity', 'time', etc.)
        offset: Number of records to skip

    Returns:
        Standard API response with dynamics data

    Example:
        GET /vtubers/dynamics?vtuber_name=星瞳&start_date=2024-01-01&end_date=2024-01-31&sort=popularity&limit=20
        Response:
        {
            "code": 200,
            "message": "Dynamics list retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "list": [
                    {
                        "name": "星瞳_Official",
                        "publish_time": "2025-05-14 17:36:07",
                        "content": "小星星们，下周的直播日程表有一点变动，520要3D和大家见面辣~！",
                        "url": "https://www.bilibili.com/opus/1066770950389760019",
                        "topic": null,
                        "dynamic_id": "1066770950389760019",
                        "share_num": 1,
                        "comment_num": 158,
                        "like_num": 1197,
                        "comment_id": "350767500",
                        "comment_type": 11,
                        "heat": 4.25
                    }
                ],
                "total_count": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        mid = get_user_mid(vtuber_name)

        if sort == "popularity" and start_date and end_date:
            # Get top dynamics by popularity
            dynamics_data = await query_top_n_dynamics(mid, start_date, end_date, limit)
        else:
            # Get all dynamics (could be extended to support other sorting)
            dynamics_data = await query_user_dynamics_by_mid(mid)
            # Apply pagination
            dynamics_data = dynamics_data[offset:offset + limit] if dynamics_data else []

        return create_success_response(
            data={
                "vtuber_name": vtuber_name,
                "mid": mid,
                "filters": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "sort": sort
                },
                "list": dynamics_data,
                "pagination": {
                    "limit": limit,
                    "offset": offset
                }
            },
            message="Dynamics retrieved successfully"
        )
    except Exception as e:
        logger.error(f"Failed to get dynamics for Vup {vtuber_name}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve dynamics", 500)
        )

@app.get("/dynamics/current")
async def read_current_dynamics(vtuber: Optional[str] = "星瞳"):
    """
    Get Vup's most recent dynamic post.

    Args:
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with the latest dynamic

    Example:
        GET /dynamics/current?vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Current dynamic retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "result": {
                    "name": "星瞳_Official",
                    "publish_time": "2025-05-14 17:36:07",
                    "content": "小星星们，下周的直播日程表有一点变动，520要3D和大家见面辣~！",
                    "url": "https://www.bilibili.com/opus/1066770950389760019",
                    "topic": null,
                    "dynamic_id": "1066770950389760019",
                    "share_num": 1,
                    "comment_num": 158,
                    "like_num": 1197,
                    "comment_id": "350767500",
                    "comment_type": 11
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query current dynamic
        current_dynamic_raw = await query_current_dynamics(mid)

        if not current_dynamic_raw or all(item is None for item in current_dynamic_raw):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No current dynamic found for Vup '{vtuber}'", 404)
            )

        # Convert raw list data to structured format
        current_dynamic = None
        if len(current_dynamic_raw) >= 11:
            current_dynamic = {
                "name": current_dynamic_raw[0],
                "publish_time": current_dynamic_raw[1],
                "content": current_dynamic_raw[2],
                "url": current_dynamic_raw[3],
                "topic": current_dynamic_raw[4],
                "dynamic_id": current_dynamic_raw[5],
                "share_num": current_dynamic_raw[6],
                "comment_num": current_dynamic_raw[7],
                "like_num": current_dynamic_raw[8],
                "comment_id": current_dynamic_raw[9],
                "comment_type": current_dynamic_raw[10]
            }

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "result": current_dynamic,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Current dynamic retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get current dynamic for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve current dynamic", 500)
        )

@app.get("/video/list")
async def read_video_list(
    vtuber: Optional[str] = "星瞳",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 10,
    sort: str = "popularity",
    offset: int = 0
):
    """
    Get Vup's video list with filtering and sorting options.

    Args:
        vtuber: Vup name (default: "星瞳")
        start_date: Start date filter (YYYY-MM-DD format)
        end_date: End date filter (YYYY-MM-DD format)
        limit: Maximum number of videos to return
        sort: Sort order ('popularity', 'time', etc.)
        offset: Number of records to skip

    Returns:
        Standard API response with video list

    Example:
        GET /video/list?vtuber=星瞳&start_date=2024-01-01&end_date=2024-01-31&sort=popularity&limit=20

        Response:
        {
            "code": 200,
            "message": "Video list retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "filters": {
                    "start_date": "2024-01-01",
                    "end_date": "2024-01-31",
                    "sort": "popularity"
                },
                "list": [
                    {
                        "name": "星瞳_Official",
                        "bvid": "BV1iSVqzDE2b",
                        "title": "一秒回到放假前！！！！",
                        "description": "表演/出镜：星瞳 剪辑：工具人 软件：基于UE5自研 动作：动作捕捉",
                        "cover": "http://i1.hdslb.com/bfs/archive/1e8938df5b9b92f9d38b40c380bca63f1eaa69a9.jpg",
                        "publish_time": "2025-05-05 18:00:00",
                        "play_num": 37809,
                        "comment_num": 196,
                        "like_num": 3710,
                        "coin": 1084,
                        "favorite_num": 494,
                        "share_num": 88,
                        "danmaku_num": 29,
                        "aid": "114453983005561",
                        "length": "00:07",
                        "honor_short": "",
                        "honor_count": 0,
                        "honor": "{}",
                        "video_ai_conclusion": "",
                        "heat": 5.24
                    }
                ],
                "pagination": {
                    "limit": 20,
                    "offset": 0
                },
                "total_count": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query video list with dynamic processing
        if sort == "popularity" and start_date and end_date:
            # Get top videos by popularity
            videos_data = await query_top_n_videos(mid, start_date, end_date, limit)
            # Convert to structured format for consistency
            structured_videos = videos_data
        else:
            # Get all videos (could be extended to support other sorting)
            videos_raw = await query_all_video_list_by_mid(mid)
            # Apply pagination for non-popularity sorting
            structured_videos = videos_raw[offset:offset + limit] if videos_raw else []

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "filters": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "sort": sort
                },
                "list": structured_videos,
                "pagination": {
                    "limit": limit,
                    "offset": offset
                },
                "total_count": len(structured_videos),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Video list retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get video list for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video list", 500)
        )


@app.get("/video/current")
async def read_current_video(vtuber: Optional[str] = "星瞳"):
    """
    Get Vup's most recent video post.

    Args:
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with the latest video

    Example:
        GET /video/current?vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Current video retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "result": {
                    "bvid": "BV1iSVqzDE2b",
                    "title": "一秒回到放假前！！！！",
                    "description": "表演/出镜：星瞳 剪辑：工具人 软件：基于UE5自研 动作：动作捕捉",
                    "cover": "http://i1.hdslb.com/bfs/archive/1e8938df5b9b92f9d38b40c380bca63f1eaa69a9.jpg",
                    "publish_time": "2025-05-05 18:00:00",
                    "play_num": 37809,
                    "comment_num": 196,
                    "like_num": 3710,
                    "coin": 1084,
                    "favorite_num": 494,
                    "share_num": 88,
                    "danmaku_num": 29,
                    "aid": "114453983005561",
                    "length": "00:07",
                    "honor_short": "",
                    "honor_count": 0,
                    "honor": "{}",
                    "video_ai_conclusion": ""
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query current video
        current_video_raw = await query_current_videos(mid)

        if not current_video_raw or all(item is None for item in current_video_raw):
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No current video found for Vup '{vtuber}'", 404)
            )

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "result": current_video_raw,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Current video retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get current video for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve current video", 500)
        )


@app.get("/comment/video")
async def read_comment_video(
    vtuber: Optional[str] = "星瞳",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 10,
    sort: str = "popularity",
    offset: int = 0
):
    """
    Get video comments for a Vup with filtering and pagination options.

    Args:
        vtuber: Vup name (default: "星瞳")
        start_date: Start date filter (YYYY-MM-DD format)
        end_date: End date filter (YYYY-MM-DD format)
        limit: Maximum number of comments to return
        sort: Sort order ('time', 'popularity', etc.)
        offset: Number of records to skip

    Returns:
        Standard API response with video comments list

    Example:
        GET /comment/video?vtuber=星瞳&start_date=2024-01-01&end_date=2024-01-31&limit=20

        Response:
        {
            "code": 200,
            "message": "Video comments retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "filters": {
                    "start_date": "2024-01-01",
                    "end_date": "2024-01-31",
                    "sort": "time"
                },
                "list": [
                    {
                        "up_name": "星瞳",
                        "commenter_name": "冲浪的阿昆达",
                        "comment": "[星瞳·表情包_大头大眼]",
                        "publish_time": "2025-05-19 22:24:28",
                        "like_num": 0,
                        "commenter_mid": "17951163",
                        "rpid": "262255884241",
                        "commenter_face": "https://i0.hdslb.com/bfs/face/1b3c206821230ecab84cc2efa4995ba30346839e.jpg",
                        "reply_count": 0,
                        "parent_rpid": "261063571617",
                        "video_oid": "114448211641820",
                        "heat": 0,
                        "sentiment": 0.1709
                    }
                ],
                "pagination": {
                    "limit": 20,
                    "offset": 0
                },
                "total_count": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query video comments with dynamic processing
        if sort == "popularity" and start_date and end_date:
            # Get top comments by popularity (using existing function)
            comments_data = await query_top_n_comments(mid, start_date, end_date, limit, "video")
            # Convert to structured format for consistency
            structured_comments = []
            for comment in comments_data:
                structured_comments.append({
                    "up_name": vtuber,
                    "commenter_name": comment.get("name", ""),
                    "comment": comment.get("comment", ""),
                    "publish_time": comment.get("time", ""),
                    "like_num": comment.get("heat", 0),
                    "commenter_mid": comment.get("uid", ""),
                    "rpid": "",
                    "commenter_face": comment.get("face", ""),
                    "reply_count": 0,
                    "parent_rpid": "",
                    "video_oid": comment.get("from_id", ""),
                    "heat": comment.get("heat", 0),
                    "sentiment": None
                })
        else:
            # Get all comments (could be extended to support other sorting)
            comments_raw = await query_all_video_comments_by_mid(mid)

            # Convert raw list data to structured format
            structured_comments = []
            for comment_row in comments_raw:
                if len(comment_row) >= 13:  # Ensure we have all expected fields
                    structured_comments.append({
                        "up_name": comment_row[0],
                        "commenter_name": comment_row[1],
                        "comment": comment_row[2],
                        "publish_time": comment_row[3],
                        "like_num": comment_row[4],
                        "commenter_mid": comment_row[5],
                        "rpid": comment_row[6],
                        "commenter_face": comment_row[7],
                        "reply_count": comment_row[8],
                        "parent_rpid": comment_row[9],
                        "video_oid": comment_row[10],
                        "heat": comment_row[11],
                        "sentiment": comment_row[12] if len(comment_row) > 12 else None
                    })

            # Apply pagination for non-popularity sorting
            structured_comments = structured_comments[offset:offset + limit] if structured_comments else []

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "filters": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "sort": sort
                },
                "list": structured_comments,
                "pagination": {
                    "limit": limit,
                    "offset": offset
                },
                "total_count": len(structured_comments),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Video comments retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get video comments for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video comments", 500)
        )


@app.get("/comment/dynamic")
async def read_comment_dynamic(
    vtuber: Optional[str] = "星瞳",
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    limit: int = 10,
    sort: str = "popularity",
    offset: int = 0
):
    """
    Get dynamic comments for a Vup with filtering and pagination options.

    Args:
        vtuber: Vup name (default: "星瞳")
        start_date: Start date filter (YYYY-MM-DD format)
        end_date: End date filter (YYYY-MM-DD format)
        limit: Maximum number of comments to return
        sort: Sort order ('time', 'popularity', etc.)
        offset: Number of records to skip

    Returns:
        Standard API response with dynamic comments list

    Example:
        GET /comment/dynamic?vtuber=星瞳&start_date=2024-01-01&end_date=2024-01-31&limit=20

        Response:
        {
            "code": 200,
            "message": "Dynamic comments retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "filters": {
                    "start_date": "2024-01-01",
                    "end_date": "2024-01-31",
                    "sort": "time"
                },
                "list": [
                    {
                        "up_name": "星瞳",
                        "commenter_name": "星闪双瞳",
                        "comment": "瞳姐拍的时候自己都憋不住笑了[百变星瞳_大笑]",
                        "publish_time": "2024-10-18 04:21:06",
                        "like_num": 703,
                        "commenter_mid": "37824774",
                        "rpid": "244649170609",
                        "commenter_face": "https://i0.hdslb.com/bfs/face/example.jpg",
                        "reply_count": 14,
                        "parent_rpid": "0",
                        "dynamic_oid": "114448211641820",
                        "heat": 717,
                        "sentiment": 0.8
                    }
                ],
                "pagination": {
                    "limit": 20,
                    "offset": 0
                },
                "total_count": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query dynamic comments with dynamic processing
        if sort == "popularity" and start_date and end_date:
            # Get top comments by popularity (using existing function)
            comments_data = await query_top_n_comments(mid, start_date, end_date, limit, "dynamic")
            # Convert to structured format for consistency
            structured_comments = []
            for comment in comments_data:
                structured_comments.append({
                    "up_name": vtuber,
                    "commenter_name": comment.get("name", ""),
                    "comment": comment.get("comment", ""),
                    "publish_time": comment.get("time", ""),
                    "like_num": comment.get("heat", 0),
                    "commenter_mid": comment.get("uid", ""),
                    "rpid": "",
                    "commenter_face": comment.get("face", ""),
                    "reply_count": 0,
                    "parent_rpid": "",
                    "dynamic_oid": comment.get("from_id", ""),
                    "heat": comment.get("heat", 0),
                    "sentiment": None
                })
        else:
            # Get all comments (could be extended to support other sorting)
            comments_raw = await query_all_dynamics_comments_by_mid(mid)

            # Convert raw list data to structured format
            structured_comments = []
            for comment_row in comments_raw:
                if len(comment_row) >= 13:  # Ensure we have all expected fields
                    structured_comments.append({
                        "up_name": comment_row[0],
                        "commenter_name": comment_row[1],
                        "comment": comment_row[2],
                        "publish_time": comment_row[3],
                        "like_num": comment_row[4],
                        "commenter_mid": comment_row[5],
                        "rpid": comment_row[6],
                        "commenter_face": comment_row[7],
                        "reply_count": comment_row[8],
                        "parent_rpid": comment_row[9],
                        "dynamic_oid": comment_row[10],
                        "heat": comment_row[11],
                        "sentiment": comment_row[12] if len(comment_row) > 12 else None
                    })

            # Apply pagination for non-popularity sorting
            structured_comments = structured_comments[offset:offset + limit] if structured_comments else []

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "filters": {
                    "start_date": start_date,
                    "end_date": end_date,
                    "sort": sort
                },
                "list": structured_comments,
                "pagination": {
                    "limit": limit,
                    "offset": offset
                },
                "total_count": len(structured_comments),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Dynamic comments retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get dynamic comments for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve dynamic comments", 500)
        )


@app.get("/comment/top_n")
async def read_comment_top_n(
    s: str, e: str, n: int, source: str = "video", vtuber: Optional[str] = "星瞳"
):
    """
    Get top N comments by heat (likes) within a specified time range.

    Args:
        s: Start date in YYYY-MM-DD format
        e: End date in YYYY-MM-DD format
        n: Number of top comments to retrieve
        source: Comment source ("video", "dynamic", "all") (default: "video")
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with top N comments by heat

    Example:
        GET /comment/top_n/?vtuber=星瞳&s=2023-01-01&e=2024-12-31&n=10&source=video

        Response:
        {
            "code": 200,
            "message": "Top N comments by heat retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "query_params": {
                    "start_date": "2023-01-01",
                    "end_date": "2024-12-31",
                    "limit": 10,
                    "source": "video",
                    "period_days": 365
                },
                "list": [
                    {
                        "name": "洛恩佐Lorenzo",
                        "uid": 1097927383,
                        "face": "https://i0.hdslb.com/bfs/face/example.jpg",
                        "time": "2024-08-31 12:09:03",
                        "comment": "太专业啦，希望以后还能在别的国产游戏里听到星瞳的配音[猴哥]",
                        "heat": 3449,
                        "from_id": "BV1JS421Q7rX",
                        "from_name": "【黑神话:悟空】虚拟偶像给国产3A配音？超长角色配音纪实！",
                        "source": "video"
                    }
                ],
                "total_count": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not s or not s.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 's' (start date) cannot be empty", 400)
            )

        if not e or not e.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'e' (end date) cannot be empty", 400)
            )

        if n <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'n' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        if source not in ["video", "dynamic", "all"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'source' must be one of: video, dynamic, all", 400)
            )

        # Validate date format and calculate period
        try:
            start_date = datetime.strptime(s, "%Y-%m-%d")
            end_date = datetime.strptime(e, "%Y-%m-%d")
            period_days = (end_date - start_date).days

            if period_days < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=create_error_response("End date must be after start date", 400)
                )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as ex:
            logger.error(f"Vup {vtuber} not found: {ex}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query top N comments
        top_comments = await query_top_n_comments(mid, s, e, n, source)

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "query_params": {
                    "start_date": s,
                    "end_date": e,
                    "limit": n,
                    "source": source,
                    "period_days": period_days
                },
                "list": top_comments,
                "total_count": len(top_comments),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Top N comments by heat retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as ex:
        logger.error(f"Failed to get top {n} comments for Vup {vtuber}: {ex}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve top N comments", 500)
        )


@app.get("/comment/top_n_user")
async def read_comment_top_n_user(
    s: str, e: str, n: int, source: str = "all", vtuber: Optional[str] = "星瞳"
):
    """
    Get top N most active commenting users within a specified time range.

    Args:
        s: Start date in YYYY-MM-DD format
        e: End date in YYYY-MM-DD format
        n: Number of top users to retrieve for each category
        source: Comment source ("video", "dynamic", "all") (default: "all")
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with top N users by different metrics

    Example:
        GET /comment/top_n_user/?s=2023-01-01&e=2024-12-31&n=10&source=all&vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Top N commenting users retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "query_params": {
                    "start_date": "2023-01-01",
                    "end_date": "2024-12-31",
                    "limit": 10,
                    "source": "all",
                    "period_days": 365
                },
                "list": {
                    "top_likes_users": [
                        {
                            "name": "雪糕cheese",
                            "likeNum": 3841,
                            "uid": "3493139945884106",
                            "face": "https://i2.hdslb.com/bfs/face/311fd02cad5db2cc33a1926b79cc9c7dc385b673.jpg"
                        }
                    ],
                    "top_comments_users": [
                        {
                            "name": "一条宇宙咸",
                            "appealNum": 156,
                            "uid": "385314951",
                            "face": "https://i0.hdslb.com/bfs/face/ad95fabe16cde205b32d6f7fd88f142b33f92fe6.jpg"
                        }
                    ],
                    "top_replies_users": [
                        {
                            "name": "活跃用户",
                            "rcountSum": 89,
                            "uid": "123456789",
                            "face": "https://i0.hdslb.com/bfs/face/example.jpg"
                        }
                    ]
                },
                "total_count": {
                    "total_likes_users": 1,
                    "total_comments_users": 1,
                    "total_replies_users": 1
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not s or not s.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 's' (start date) cannot be empty", 400)
            )

        if not e or not e.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'e' (end date) cannot be empty", 400)
            )

        if n <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'n' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        if source not in ["video", "dynamic", "all"]:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'source' must be one of: video, dynamic, all", 400)
            )

        # Validate date format and calculate period
        try:
            start_date = datetime.strptime(s, "%Y-%m-%d")
            end_date = datetime.strptime(e, "%Y-%m-%d")
            period_days = (end_date - start_date).days

            if period_days < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=create_error_response("End date must be after start date", 400)
                )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as ex:
            logger.error(f"Vup {vtuber} not found: {ex}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query top N commenting users
        top_likes, top_comments, top_replies = await query_top_n_comments_user(
            mid, s, e, n, source
        )

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "query_params": {
                    "start_date": s,
                    "end_date": e,
                    "limit": n,
                    "source": source,
                    "period_days": period_days
                },
                "list": {
                    "top_likes_users": top_likes,
                    "top_comments_users": top_comments,
                    "top_replies_users": top_replies
                },
                "total_count": {
                    "total_likes_users": len(top_likes),
                    "total_comments_users": len(top_comments),
                    "total_replies_users": len(top_replies)
                },
                "retrieved_at": datetime.now().isoformat()
            },
            message="Top N commenting users retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as ex:
        logger.error(f"Failed to get top {n} commenting users for Vup {vtuber}: {ex}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve top N commenting users", 500)
        )


@app.get("/comment/wordcloud")
async def read_comment_wordcloud(
    s: str,
    e: str,
    vtuber: Optional[str] = "星瞳",
    format: Optional[str] = "image"
):
    """
    Generate and return word cloud image from comments within a specified time range.

    Args:
        s: Start date in YYYY-MM-DD format
        e: End date in YYYY-MM-DD format
        vtuber: Vup name (default: "星瞳")
        format: Response format - "image" for direct PNG (default) or "json" for metadata

    Returns:
        - Default: Direct PNG image file response with caching headers
        - If format="json": Metadata response for debugging

    Examples:
        GET /comment/wordcloud/?vtuber=星瞳&s=2023-01-01&e=2024-12-31
        GET /comment/wordcloud/?vtuber=星瞳&s=2023-01-01&e=2024-12-31&format=image
        GET /comment/wordcloud/?vtuber=星瞳&s=2023-01-01&e=2024-12-31&format=json

    Usage in HTML:
        <img src="/comment/wordcloud/?vtuber=星瞳&s=2023-01-01&e=2024-12-31" alt="Word Cloud">
    """
    try:
        # Parameter validation
        if not s or not s.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 's' (start date) cannot be empty", 400)
            )

        if not e or not e.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'e' (end date) cannot be empty", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Validate date format and calculate period
        try:
            start_date = datetime.strptime(s, "%Y-%m-%d")
            end_date = datetime.strptime(e, "%Y-%m-%d")
            period_days = (end_date - start_date).days

            if period_days < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=create_error_response("End date must be after start date", 400)
                )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Get Vup MID and Chinese name
        try:
            mid = get_user_mid(vtuber)
            char_zh = U.get_zh_role_name(vtuber)
        except Exception as ex:
            logger.error(f"Vup {vtuber} not found: {ex}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Generate word cloud with intelligent caching
        wordcloud_result = await query_comment_wordcloud(mid, char_zh, s, e)

        if wordcloud_result is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No comments found for Vup '{vtuber}' in the specified time range", 404)
            )

        wordcloud_path, is_cached = wordcloud_result

        # Handle different response formats
        if format == "json":
            # Return JSON metadata for debugging
            base_url = "http://localhost:9022"  # This should be configurable
            image_url = f"{base_url}{wordcloud_path}"

            return create_success_response(
                data={
                    "vtuber_name": vtuber,
                    "mid": mid,
                    "query_params": {
                        "start_date": s,
                        "end_date": e,
                        "period_days": period_days
                    },
                    "result": {
                        "image_path": wordcloud_path,
                        "image_url": image_url,
                        "generated_at": datetime.now().isoformat(),
                        "cached": is_cached
                    },
                    "retrieved_at": datetime.now().isoformat()
                },
                message="Word cloud generated successfully"
            )
        else:
            # Return image file directly (default)
            full_image_path = f"frontend/public{wordcloud_path}"
            if os.path.exists(full_image_path):
                # Get file modification time for caching headers
                file_stat = os.stat(full_image_path)
                last_modified = datetime.fromtimestamp(file_stat.st_mtime)

                return FileResponse(
                    path=full_image_path,
                    media_type="image/png",
                    filename=f"{char_zh}_wordcloud.png",
                    headers={
                        "Cache-Control": "public, max-age=86400",  # 24 hours
                        "Last-Modified": last_modified.strftime("%a, %d %b %Y %H:%M:%S GMT"),
                        "ETag": f'"{hash(f"{wordcloud_path}_{file_stat.st_mtime}")}"',
                        "X-Cache-Status": "HIT" if is_cached else "MISS"
                    }
                )
            else:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=create_error_response("Generated word cloud file not found", 500)
                )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as ex:
        logger.error(f"Failed to generate word cloud for Vup {vtuber}: {ex}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to generate word cloud", 500)
        )


@app.get("/tieba/whole")
async def read_whole_tieba(s: str, e: str, vtuber: Optional[str] = "星瞳"):
    """
    Get complete Tieba (forum) data including posts and comments within a specified time range.

    Args:
        s: Start date in YYYY-MM-DD format
        e: End date in YYYY-MM-DD format
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with Tieba posts and comments

    Example:
        GET /tieba/whole/?vtuber=星瞳&s=2023-01-01&e=2024-12-31

        Response:
        {
            "code": 200,
            "message": "Tieba data retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "query_params": {
                    "start_date": "2023-01-01",
                    "end_date": "2024-12-31",
                    "period_days": 365
                },
                "list": [
                    {
                        "fid": "27776437",
                        "fname": "星瞳official",
                        "tid": "9015559022",
                        "user_name": "🍺打翻酱油瓶",
                        "create_time": "2024-05-13 23:01:28",
                        "last_time": "2024-05-13 23:01:28",
                        "title": "求助 请问谁有星瞳这个表情的图",
                        "text": "突然get到这个图的点 喜欢想用来当头像 有图的分享一下吧 谢谢大佬们",
                        "img": "https://tiebapic.baidu.com/forum/w%3D720%3Bq%3D60%3Bg%3D0/sign=de8d64f7173d26972ed30a5f65c0c3c6/5008401090ef76c6d98ea216db16fdfaae516782.jpg",
                        "view_num": 990,
                        "reply_num": 0,
                        "agree": 3,
                        "disagree": 0,
                        "level_num": 1,
                        "pid": "150271963267",
                        "floor": 1
                    }
                ],
                "total_count": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not s or not s.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 's' (start date) cannot be empty", 400)
            )

        if not e or not e.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'e' (end date) cannot be empty", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Validate date format and calculate period
        try:
            start_date = datetime.strptime(s, "%Y-%m-%d")
            end_date = datetime.strptime(e, "%Y-%m-%d")
            period_days = (end_date - start_date).days

            if period_days < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=create_error_response("End date must be after start date", 400)
                )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as ex:
            logger.error(f"Vup {vtuber} not found: {ex}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query Tieba data
        tieba_raw = await query_tieba_whole(mid, s, e)

        # Convert raw list data to structured format
        structured_tieba = []
        for tieba_row in tieba_raw:
            if len(tieba_row) >= 16:  # Ensure we have all expected fields
                structured_tieba.append({
                    "fid": tieba_row[0],
                    "fname": tieba_row[1],
                    "tid": tieba_row[2],
                    "user_name": tieba_row[3],
                    "create_time": tieba_row[4],
                    "last_time": tieba_row[5],
                    "title": tieba_row[6],
                    "text": tieba_row[7],
                    "img": tieba_row[8],
                    "view_num": tieba_row[9],
                    "reply_num": tieba_row[10],
                    "agree": tieba_row[11],
                    "disagree": tieba_row[12],
                    "level_num": tieba_row[13],
                    "pid": tieba_row[14],
                    "floor": tieba_row[15]
                })

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "query_params": {
                    "start_date": s,
                    "end_date": e,
                    "period_days": period_days
                },
                "list": structured_tieba,
                "total_count": len(structured_tieba),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Tieba data retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as ex:
        logger.error(f"Failed to get Tieba data for Vup {vtuber}: {ex}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve Tieba data", 500)
        )


@app.get("/tieba/thread")
async def read_thread_tieba(s: str, e: str, vtuber: Optional[str] = "星瞳"):
    """
    Get Tieba thread posts within a specified time range.

    Args:
        s: Start date in YYYY-MM-DD format
        e: End date in YYYY-MM-DD format
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with Tieba thread posts

    Example:
        GET /tieba/thread/?vtuber=星瞳&s=2023-01-01&e=2024-12-31

        Response:
        {
            "code": 200,
            "message": "Tieba thread posts retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "query_params": {
                    "start_date": "2023-01-01",
                    "end_date": "2024-12-31",
                    "period_days": 365
                },
                "list": [
                    {
                        "fid": "27776437",
                        "fname": "星瞳official",
                        "tid": "9014779954",
                        "user_name": "只是只猹_",
                        "create_time": "2024-05-13 11:19:56",
                        "last_time": "2024-05-18 12:34:47",
                        "title": "已被主播眼熟",
                        "text": "已被主播眼熟\\n望周知",
                        "img": "http://tiebapic.baidu.com/forum/w%3D720%3Bq%3D60%3Bg%3D0/sign=2d7efa30bedcd100cd9cfa2342b0362d/9dedb831e924b899bfb8290528061d950b7bf65a.jpg",
                        "view_num": 3584,
                        "reply_num": 25,
                        "share_num": 0,
                        "agree": 48,
                        "disagree": 0
                    }
                ],
                "total_count": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not s or not s.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 's' (start date) cannot be empty", 400)
            )

        if not e or not e.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'e' (end date) cannot be empty", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Validate date format and calculate period
        try:
            start_date = datetime.strptime(s, "%Y-%m-%d")
            end_date = datetime.strptime(e, "%Y-%m-%d")
            period_days = (end_date - start_date).days

            if period_days < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=create_error_response("End date must be after start date", 400)
                )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as ex:
            logger.error(f"Vup {vtuber} not found: {ex}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query Tieba thread posts
        threads_raw = await query_tieba_threads(mid, s, e)

        # Convert raw list data to structured format
        structured_threads = []
        for thread_row in threads_raw:
            if len(thread_row) >= 14:  # Ensure we have all expected fields
                structured_threads.append({
                    "fid": thread_row[0],
                    "fname": thread_row[1],
                    "tid": thread_row[2],
                    "user_name": thread_row[3],
                    "create_time": thread_row[4],
                    "last_time": thread_row[5],
                    "title": thread_row[6],
                    "text": thread_row[7],
                    "img": thread_row[8],
                    "view_num": thread_row[9],
                    "reply_num": thread_row[10],
                    "share_num": thread_row[11],
                    "agree": thread_row[12],
                    "disagree": thread_row[13]
                })

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "query_params": {
                    "start_date": s,
                    "end_date": e,
                    "period_days": period_days
                },
                "list": structured_threads,
                "total_count": len(structured_threads),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Tieba thread posts retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as ex:
        logger.error(f"Failed to get Tieba thread posts for Vup {vtuber}: {ex}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve Tieba thread posts", 500)
        )


@app.get("/llm/relations")
async def read_recent_relationships(recent: int = 30, vtuber: Optional[str] = "星瞳"):
    """
    Get recent relationship and collaboration information for a Vup.

    Args:
        recent: Number of recent days to analyze (default: 30)
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with relationship information

    Example:
        GET /llm/relations?vtuber=星瞳&recent=30

        Response:
        {
            "code": 200,
            "message": "Recent relationships retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "analysis_params": {
                    "recent_days": 30,
                    "target_date": "2025-08-04"
                },
                "list": [
                    "等一下，拯救者！中秋和扇宝一起拍'打歌视频'!嘎嘎~",
                    "俏鸡传说原创专辑的第二首合唱曲《化身拯救者靠泡泡糖消灭黑暗怪兽》PV上线喽",
                    "星瞳X扇宝的《倾城第一花》同名收藏集开启预约啦！",
                    "宜宝诺宝周年庆啦，祝快乐！"
                ],
                "total_count": 4,
                "has_data": true,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query recent relationships
        cur_datetime = datetime.now().strftime("%Y-%m-%d")
        relationships_json = await query_recent_relationships(mid, cur_datetime, recent)

        # Parse relationships data
        relationships_list = []
        has_data = False

        if relationships_json:
            try:
                relationships_data = json.loads(relationships_json)
                if isinstance(relationships_data, list):
                    relationships_list = relationships_data
                    has_data = True
                elif isinstance(relationships_data, dict) and "relationships" in relationships_data:
                    relationships_list = relationships_data["relationships"]
                    has_data = True
                else:
                    relationships_list = [str(relationships_data)]
                    has_data = True
            except json.JSONDecodeError as e:
                logger.warning(f"Failed to parse relationships JSON for Vup {vtuber}: {e}")
                relationships_list = ["数据解析失败"]
                has_data = False
        else:
            relationships_list = ["暂未生成联动信息"]
            has_data = False

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "analysis_params": {
                    "recent_days": recent,
                    "target_date": cur_datetime
                },
                "list": relationships_list,
                "total_count": len(relationships_list),
                "has_data": has_data,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Recent relationships retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get recent relationships for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve recent relationships", 500)
        )


@app.get("/llm/sensiment")
async def read_recent_sensiment(recent: int = 30, vtuber: Optional[str] = "星瞳"):
    """
    Get recent comment sentiment analysis for a Vup from all sources.

    Args:
        recent: Number of recent days to analyze (default: 30)
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with sentiment analysis data

    Example:
        GET /llm/sensiment/?vtuber=星瞳&recent=30

        Response:
        {
            "code": 200,
            "message": "Recent sentiment analysis retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "analysis_params": {
                    "recent_days": 30,
                    "target_date": "2025-08-04",
                    "source": "all"
                },
                "result": {
                    "love_ratio": 0.1,
                    "positive_ratio": 0.2,
                    "neutral_ratio": 0.3,
                    "critical_ratio": 0.2,
                    "negative_ratio": 0.2
                },
                "detailed_info": [
                    {
                        "time": "2023-01-01",
                        "comment": ["Great product!", "Really liked it."],
                        "sentiment": 0.85
                    },
                    {
                        "time": "2023-01-02",
                        "comment": ["Not bad.", "Could be better."],
                        "sentiment": 0.55
                    }
                ],
                "total_count": 2,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query sentiment analysis
        cur_time = datetime.now().strftime("%Y-%m-%d")
        sentiment_data = await query_recent_comments_sentiment_value(
            mid, cur_time, recent, source="all"
        )

        if sentiment_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No sentiment data found for Vup '{vtuber}' in the last {recent} days", 404)
            )

        # Extract sentiment ratios and detailed info
        sentiment_analysis = {
            "love_ratio": sentiment_data.get("love_ratio", 0),
            "positive_ratio": sentiment_data.get("positive_ratio", 0),
            "neutral_ratio": sentiment_data.get("neutral_ratio", 0),
            "critical_ratio": sentiment_data.get("critical_ratio", 0),
            "negative_ratio": sentiment_data.get("negative_ratio", 0),
            "info": sentiment_data.get("info", [])
        }

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "analysis_params": {
                    "recent_days": recent,
                    "target_date": cur_time,
                    "source": "all"
                },
                "result": sentiment_analysis,
                "total_count": len(sentiment_analysis.get("info", [])),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Recent sentiment analysis retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get recent sentiment analysis for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve recent sentiment analysis", 500)
        )


@app.get("/llm/sensiment/bili")
async def read_recent_sensiment_bili(recent: int = 30, vtuber: Optional[str] = "星瞳"):
    """
    Get recent comment sentiment analysis for a Vup from Bilibili sources only.

    Args:
        recent: Number of recent days to analyze (default: 30)
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with Bilibili sentiment analysis data

    Example:
        GET /llm/sensiment/bili?vtuber=星瞳&recent=30

        Response:
        {
            "code": 200,
            "message": "Recent Bilibili sentiment analysis retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "analysis_params": {
                    "recent_days": 30,
                    "target_date": "2025-08-04",
                    "source": "bili"
                },
                "result": {
                    "love_ratio": 0.1,
                    "positive_ratio": 0.2,
                    "neutral_ratio": 0.3,
                    "critical_ratio": 0.2,
                    "negative_ratio": 0.2
                },
                "detailed_info": [
                    {
                        "time": "2023-01-01",
                        "comment": ["Great product!", "Really liked it."],
                        "sentiment": 0.85
                    }
                ],
                "total_entries": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query Bilibili sentiment analysis
        cur_time = datetime.now().strftime("%Y-%m-%d")
        sentiment_data = await query_recent_comments_sentiment_value(
            mid, cur_time, recent, source="bili"
        )

        if sentiment_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No Bilibili sentiment data found for Vup '{vtuber}' in the last {recent} days", 404)
            )

        # Extract sentiment ratios and detailed info
        sentiment_analysis = {
            "love_ratio": sentiment_data.get("love_ratio", 0),
            "positive_ratio": sentiment_data.get("positive_ratio", 0),
            "neutral_ratio": sentiment_data.get("neutral_ratio", 0),
            "critical_ratio": sentiment_data.get("critical_ratio", 0),
            "negative_ratio": sentiment_data.get("negative_ratio", 0),
            "info": sentiment_data.get("info", [])
        }

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "analysis_params": {
                    "recent_days": recent,
                    "target_date": cur_time,
                    "source": "bili"
                },
                "result": sentiment_analysis,
                "total_count": len(sentiment_analysis.get("info", [])),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Recent Bilibili sentiment analysis retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get recent Bilibili sentiment analysis for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve recent Bilibili sentiment analysis", 500)
        )


@app.get("/llm/sensiment/tieba")
async def read_recent_sensiment_tieba(recent: int = 30, vtuber: Optional[str] = "星瞳"):
    """
    Get recent comment sentiment analysis for a Vup from Tieba sources only.

    Args:
        recent: Number of recent days to analyze (default: 30)
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with Tieba sentiment analysis data

    Example:
        GET /llm/sensiment/tieba/?vtuber=星瞳&recent=30

        Response:
        {
            "code": 200,
            "message": "Recent Tieba sentiment analysis retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "analysis_params": {
                    "recent_days": 30,
                    "target_date": "2025-08-04",
                    "source": "tieba"
                },
                "result": {
                    "love_ratio": 0.1,
                    "positive_ratio": 0.2,
                    "neutral_ratio": 0.3,
                    "critical_ratio": 0.2,
                    "negative_ratio": 0.2
                },
                "detailed_info": [
                    {
                        "time": "2023-01-01",
                        "comment": ["Great product!", "Really liked it."],
                        "sentiment": 0.85
                    }
                ],
                "total_entries": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query Tieba sentiment analysis
        cur_time = datetime.now().strftime("%Y-%m-%d")
        sentiment_data = await query_recent_comments_sentiment_value(
            mid, cur_time, recent, source="tieba"
        )

        if sentiment_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No Tieba sentiment data found for Vup '{vtuber}' in the last {recent} days", 404)
            )

        # Extract sentiment ratios and detailed info
        sentiment_analysis = {
            "love_ratio": sentiment_data.get("love_ratio", 0),
            "positive_ratio": sentiment_data.get("positive_ratio", 0),
            "neutral_ratio": sentiment_data.get("neutral_ratio", 0),
            "critical_ratio": sentiment_data.get("critical_ratio", 0),
            "negative_ratio": sentiment_data.get("negative_ratio", 0),
            "info": sentiment_data.get("info", [])
        }

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "analysis_params": {
                    "recent_days": recent,
                    "target_date": cur_time,
                    "source": "tieba"
                },
                "result": sentiment_analysis,
                "total_entries": len(sentiment_analysis.get("info", [])),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Recent Tieba sentiment analysis retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get recent Tieba sentiment analysis for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve recent Tieba sentiment analysis", 500)
        )


@app.get("/llm/topics")
async def read_comment_topics(recent: int = 30, vtuber: Optional[str] = "星瞳"):
    """
    Get recent comment topics and discussion themes for a Vup.

    Args:
        recent: Number of recent days to analyze (default: 30)
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with comment topics analysis

    Example:
        GET /llm/topics/?vtuber=星瞳&recent=30

        Response:
        {
            "code": 200,
            "message": "Comment topics retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "analysis_params": {
                    "recent_days": 30,
                    "target_date": "2025-08-04"
                },
                "list": [
                    {
                        "topic": "虚拟主播星瞳'七杀梗'引发争议",
                        "rank": 1,
                        "heat": 5,
                        "keywords": [
                            "星瞳",
                            "七杀梗",
                            "背锅侠",
                            "争议",
                            "推卸责任"
                        ],
                        "comments": [
                            "七杀梗已经让星瞳变成了传奇背锅侠...",
                            "现在的星瞳已经被描述成了一个人厌狗嫌的扫把星..."
                        ]
                    }
                ],
                "total_count": 1,
                "has_data": true,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query comment topics
        cur_datetime = datetime.now().strftime("%Y-%m-%d")
        topics_data = await query_tieba_summaries_from_ai_gen_table_by_date(
            mid, cur_datetime, recent
        )

        # Process topics data
        topics_list = []
        has_data = False

        if topics_data:
            if isinstance(topics_data, list):
                topics_list = topics_data
                has_data = True
            elif isinstance(topics_data, dict):
                # If it's a single topic object, wrap it in a list
                topics_list = [topics_data]
                has_data = True
            else:
                logger.warning(f"Unexpected topics data format for Vup {vtuber}: {type(topics_data)}")
                topics_list = []
                has_data = False
        else:
            topics_list = []
            has_data = False

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "analysis_params": {
                    "recent_days": recent,
                    "target_date": cur_datetime
                },
                "list": topics_list,
                "total_count": len(topics_list),
                "has_data": has_data,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Comment topics retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get comment topics for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve comment topics", 500)
        )


@app.get("/basic/follower/rise")
async def read_follower_arise_num(recent: int = 1, vtuber: Optional[str] = "星瞳"):
    """
    Get current follower count change for a Vup.

    Args:
        recent: Number of recent days to calculate change (default: 1)
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with follower change data

    Example:
        GET /basic/follower/rise/?vtuber=星瞳&recent=1

        Response:
        {
            "code": 200,
            "message": "Follower change retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "result": -20,
                "period_days": 1,
                "change_type": "decrease",
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query follower change
        follower_change = await query_current_follower_change_num(mid, recent)

        if follower_change is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No follower data found for Vup '{vtuber}' in the last {recent} days", 404)
            )

        # Determine change type
        change_type = "increase" if follower_change > 0 else "decrease" if follower_change < 0 else "no_change"

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "result": follower_change,
                "period_days": recent,
                "change_type": change_type,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Follower change retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get follower change for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve follower change", 500)
        )


@app.get("/basic/dahanghai/rise")
async def read_dahanghai_arise_num(recent: int = 1, vtuber: Optional[str] = "星瞳"):
    """
    Get current dahanghai (guard) count change for a Vup.

    Args:
        recent: Number of recent days to calculate change (default: 1)
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with dahanghai change data

    Example:
        GET /basic/dahanghai/rise/?vtuber=星瞳&recent=1

        Response:
        {
            "code": 200,
            "message": "Dahanghai change retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "result": -2,
                "period_days": 1,
                "change_type": "decrease",
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query dahanghai change
        dahanghai_change = await query_current_dahanghai_change_num(mid, recent)

        if dahanghai_change is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No dahanghai data found for Vup '{vtuber}' in the last {recent} days", 404)
            )

        # Determine change type
        change_type = "increase" if dahanghai_change > 0 else "decrease" if dahanghai_change < 0 else "no_change"

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "result": dahanghai_change,
                "period_days": recent,
                "change_type": change_type,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Dahanghai change retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get dahanghai change for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve dahanghai change", 500)
        )

@app.get("/video/conclusion")
async def read_video_ai_conclusion(bvid: str):
    """
    [DEPRECATED] Get video AI conclusion. Use GET /videos/{bvid}/conclusion instead.
    """
    logger.warning("Using deprecated endpoint /video/conclusion. Use GET /videos/{bvid}/conclusion instead.")
    info = await query_video_ai_conclusion_by_bvid(bvid)
    return info

@app.get("/video/views/current")
async def read_current_video_day_views(bvid: str):
    """
    [DEPRECATED] Get current video views. Use GET /videos/{bvid}/views/current instead.
    """
    logger.warning("Using deprecated endpoint /video/views/current. Use GET /videos/{bvid}/views/current instead.")
    info = await query_current_video_day_views(bvid)
    return info


@app.get("/video/views/recent")
async def read_recent_video_day_views(bvid: str, recent: int):
    """
    Get recent daily view data for a specific video.

    Args:
        bvid: Video BVID (Bilibili Video ID)
        recent: Number of recent days to retrieve data for

    Returns:
        Standard API response with recent video view data

    Example:
        GET /video/views/recent/?bvid=BV11k4y1Y71i&recent=20

        Response:
        {
            "code": 200,
            "message": "Recent video view data retrieved successfully",
            "data": {
                "bvid": "BV1a3DqYZErW",
                "query_params": {
                    "recent_days": 20
                },
                "list": [
                    {
                        "id": 63391,
                        "uid": "401315430",
                        "name": "星瞳",
                        "bvid": "BV1a3DqYZErW",
                        "title": "40秒梦幻旅行，跟我一起遨游天际！【iykyk】",
                        "create_time": 1731146400,
                        "datetime": "2025-05-29T00:00:00",
                        "view_num": 100740,
                        "view_rise_num": 4
                    }
                ],
                "total_records": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not bvid or not bvid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'bvid' cannot be empty", 400)
            )

        if recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if recent > 365:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' cannot exceed 365 days", 400)
            )

        # Validate BVID format (basic check)
        if not bvid.startswith('BV') or len(bvid) != 12:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid BVID format. BVID should start with 'BV' and be 12 characters long", 400)
            )

        # Query recent video view data
        view_data_raw = await query_single_video_rencent_day_data(bvid, recent)

        if view_data_raw is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No view data found for video '{bvid}' in the last {recent} days", 404)
            )

        # Convert raw database records to structured format
        structured_view_data = []
        for record in view_data_raw:
            if record:
                structured_view_data.append({
                    "id": record.get("id"),
                    "uid": record.get("uid"),
                    "name": record.get("name"),
                    "bvid": record.get("bvid"),
                    "title": record.get("title"),
                    "create_time": record.get("create_time"),
                    "datetime": record.get("datetime").isoformat() if record.get("datetime") else None,
                    "view_num": record.get("view_num"),
                    "view_rise_num": record.get("view_rise_num")
                })

        return create_success_response(
            data={
                "bvid": bvid,
                "query_params": {
                    "recent_days": recent
                },
                "list": structured_view_data,
                "total_records": len(structured_view_data),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Recent video view data retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get recent view data for video {bvid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve recent video view data", 500)
        )


@app.get("/video/views/target")
async def read_target_video_day_views(bvid: str, time: str):
    """
    Get daily view data for a specific video on a target date.

    Args:
        bvid: Video BVID (Bilibili Video ID)
        time: Target date in YYYY-MM-DD format

    Returns:
        Standard API response with target date video view data

    Example:
        GET /video/views/target/?bvid=BV11k4y1Y71i&time=2024-11-22

        Response:
        {
            "code": 200,
            "message": "Target date video view data retrieved successfully",
            "data": {
                "bvid": "BV1a3DqYZErW",
                "target_date": "2024-11-22",
                "list": {
                    "id": 63391,
                    "uid": "401315430",
                    "name": "星瞳",
                    "bvid": "BV1a3DqYZErW",
                    "title": "40秒梦幻旅行，跟我一起遨游天际！【iykyk】",
                    "create_time": 1731146400,
                    "datetime": "2024-11-22T00:00:00",
                    "view_num": 94865,
                    "view_rise_num": 0
                },
                "has_data": true,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not bvid or not bvid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'bvid' cannot be empty", 400)
            )

        if not time or not time.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'time' cannot be empty", 400)
            )

        # Validate BVID format (basic check)
        if not bvid.startswith('BV') or len(bvid) != 12:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid BVID format. BVID should start with 'BV' and be 12 characters long", 400)
            )

        # Validate date format
        try:
            datetime.strptime(time, "%Y-%m-%d")
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Query target date video view data
        view_data_record = await query_single_video_target_day_data(bvid, time)

        # Process the result
        has_data = view_data_record is not None
        view_data = None

        if has_data:
            view_data = {
                "id": view_data_record.get("id"),
                "uid": view_data_record.get("uid"),
                "name": view_data_record.get("name"),
                "bvid": view_data_record.get("bvid"),
                "title": view_data_record.get("title"),
                "create_time": view_data_record.get("create_time"),
                "datetime": view_data_record.get("datetime").isoformat() if view_data_record.get("datetime") else None,
                "view_num": view_data_record.get("view_num"),
                "view_rise_num": view_data_record.get("view_rise_num")
            }

        return create_success_response(
            data={
                "bvid": bvid,
                "target_date": time,
                "list": view_data,
                "has_data": has_data,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Target date video view data retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get target date view data for video {bvid} on {time}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve target date video view data", 500)
        )


@app.get("/video/views/top_n/day")
async def read_top_n_video_day_by_time(
    time: str, n: int, vtuber: Optional[str] = "星瞳"
):
    """
    Get top N videos by view count increase on a specific day.

    Args:
        time: Target date in YYYY-MM-DD format
        n: Number of top videos to retrieve
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with top N videos by view increase

    Example:
        GET /video/views/top_n/day/?time=2024-11-22&n=20&vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Top N videos by view increase retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "query_params": {
                    "target_date": "2024-11-22",
                    "limit": 20
                },
                "list": [
                    {
                        "id": 63391,
                        "uid": "401315430",
                        "name": "星瞳",
                        "bvid": "BV1a3DqYZErW",
                        "title": "40秒梦幻旅行，跟我一起遨游天际！【iykyk】",
                        "create_time": 1731146400,
                        "datetime": "2024-11-22T00:00:00",
                        "view_num": 94865,
                        "view_rise_num": 0
                    }
                ],
                "total_count": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not time or not time.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'time' cannot be empty", 400)
            )

        if n <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'n' must be a positive integer", 400)
            )

        if n > 100:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'n' cannot exceed 100", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Validate date format
        try:
            datetime.strptime(time, "%Y-%m-%d")
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query top N videos by view increase
        top_videos_raw = await query_top_n_view_rise_target_day_data(mid, time, n)

        if top_videos_raw is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No video data found for Vup '{vtuber}' on {time}", 404)
            )

        # Convert raw database records to structured format
        structured_videos = []
        for record in top_videos_raw:
            if record:
                structured_videos.append({
                    "id": record.get("id"),
                    "uid": record.get("uid"),
                    "name": record.get("name"),
                    "bvid": record.get("bvid"),
                    "title": record.get("title"),
                    "create_time": record.get("create_time"),
                    "datetime": record.get("datetime").isoformat() if record.get("datetime") else None,
                    "view_num": record.get("view_num"),
                    "view_rise_num": record.get("view_rise_num")
                })

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "query_params": {
                    "target_date": time,
                    "limit": n
                },
                "list": structured_videos,
                "total_count": len(structured_videos),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Top N videos by view increase retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get top {n} videos for Vup {vtuber} on {time}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve top N videos by view increase", 500)
        )


@app.get("/video/views/top_n/period")
async def read_top_n_video_day_by_period(s: str, e: str, n: int, vtuber: Optional[str] = "星瞳"):
    """
    Get top N videos by view count increase within a specified time period.

    Args:
        s: Start date in YYYY-MM-DD format
        e: End date in YYYY-MM-DD format
        n: Number of top videos to retrieve
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with top N videos by view increase in period

    Example:
        GET /video/views/top_n/period/?s=2024-11-21&e=2024-11-21&n=20&vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Top N videos by view increase in period retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "query_params": {
                    "start_date": "2024-11-21",
                    "end_date": "2024-11-21",
                    "limit": 20,
                    "period_days": 0
                },
                "list": [
                    {
                        "id": 63391,
                        "uid": "401315430",
                        "name": "星瞳",
                        "bvid": "BV1a3DqYZErW",
                        "title": "40秒梦幻旅行，跟我一起遨游天际！【iykyk】",
                        "create_time": 1731146400,
                        "datetime": "2024-11-21T00:00:00",
                        "view_num": 94865,
                        "view_rise_num": 0
                    }
                ],
                "total_count": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not s or not s.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 's' (start date) cannot be empty", 400)
            )

        if not e or not e.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'e' (end date) cannot be empty", 400)
            )

        if n <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'n' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Validate date format and calculate period
        try:
            start_date = datetime.strptime(s, "%Y-%m-%d")
            end_date = datetime.strptime(e, "%Y-%m-%d")
            period_days = (end_date - start_date).days

            if period_days < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=create_error_response("End date must be after or equal to start date", 400)
                )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as ex:
            logger.error(f"Vup {vtuber} not found: {ex}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query top N videos by view increase in period
        top_videos_raw = await query_top_n_view_rise_day_data_period(mid, s, e, n)

        if top_videos_raw is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No video data found for Vup '{vtuber}' in period {s} to {e}", 404)
            )

        # Convert raw database records to structured format
        structured_videos = []
        for record in top_videos_raw:
            if record:
                structured_videos.append({
                    "id": record.get("id"),
                    "uid": record.get("uid"),
                    "name": record.get("name"),
                    "bvid": record.get("bvid"),
                    "title": record.get("title"),
                    "create_time": record.get("create_time"),
                    "datetime": record.get("datetime").isoformat() if record.get("datetime") else None,
                    "view_num": record.get("view_num"),
                    "view_rise_num": record.get("view_rise_num")
                })

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "query_params": {
                    "start_date": s,
                    "end_date": e,
                    "limit": n,
                    "period_days": period_days
                },
                "list": structured_videos,
                "total_count": len(structured_videos),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Top N videos by view increase in period retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as ex:
        logger.error(f"Failed to get top {n} videos for Vup {vtuber} in period {s} to {e}: {ex}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve top N videos by view increase in period", 500)
        )


@app.get("/basic/dahanghai/list/target")
async def read_target_dahanghai_whole_list(
    target_datetime: str, vtuber: Optional[str] = "星瞳"
):
    """
    Get dahanghai (guard) list for a specific Vup on a target date.

    Args:
        target_datetime: Target date in YYYY-MM-DD format
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with dahanghai list data

    Example:
        GET /basic/dahanghai/list/target/?target_datetime=2025-05-14&vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Dahanghai list retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "target_date": "2025-05-14",
                "list": [
                    {
                        "up_uid": "401315430",
                        "up_name": "星瞳",
                        "time": "2025-05-14",
                        "datetime": "2025-05-14T15:19:53.561790",
                        "num": 276,
                        "page": 1,
                        "uid": 2168222,
                        "ruid": 401315430,
                        "rank": 1,
                        "username": "bili_2976",
                        "face": "https://i0.hdslb.com/bfs/face/member/noface.jpg",
                        "guard_level": 1,
                        "guard_sub_level": 0,
                        "if_top3": true
                    }
                ],
                "total_count": 1,
                "has_data": true,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not target_datetime or not target_datetime.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'target_datetime' cannot be empty", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Validate date format
        try:
            datetime.strptime(target_datetime, "%Y-%m-%d")
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query dahanghai list for target date
        dahanghai_records = await query_dahanghai_list_by_uid_and_datetime(mid, target_datetime)

        # Process the result
        has_data = dahanghai_records is not None and len(dahanghai_records) > 0
        dahanghai_list = []

        if has_data:
            for record in dahanghai_records:
                dahanghai_list.append({
                    "up_uid": record.get("up_uid"),
                    "up_name": record.get("up_name"),
                    "time": record.get("time").strftime("%Y-%m-%d") if record.get("time") else None,
                    "datetime": record.get("datetime").isoformat() if record.get("datetime") else None,
                    "num": record.get("num"),
                    "page": record.get("page"),
                    "uid": record.get("uid"),
                    "ruid": record.get("ruid"),
                    "rank": record.get("rank"),
                    "username": record.get("username"),
                    "face": record.get("face"),
                    "guard_level": record.get("guard_level"),
                    "guard_sub_level": record.get("guard_sub_level"),
                    "if_top3": record.get("if_top3")
                })

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "target_date": target_datetime,
                "list": dahanghai_list,
                "total_count": len(dahanghai_list),
                "has_data": has_data,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Dahanghai list retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get dahanghai list for Vup {vtuber} on {target_datetime}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve dahanghai list", 500)
        )


@app.get("/info/recent/dict")
async def read_recent_info(vtuber: Optional[str] = "星瞳"):
    """
    Get comprehensive recent activity information for a Vup.

    Args:
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with comprehensive recent activity data

    Example:
        GET /info/recent/dict/?vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Recent activity information retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "room_id": "22625025",
                "result": {
                    "time": "2025-05-28",
                    "name": "星瞳_Official",
                    "follower_change": -44,
                    "dahanghai_change": -1,
                    "video_content": [
                        "2025-05-27 18:32:29",
                        "打CALL教程❌体能测试✅",
                        "14100",
                        ""
                    ],
                    "dynamic_content": [
                        "2025-05-27 18:32:29",
                        "叮铃铃~在充满爱的起床铃中举起了双手~闪闪发光的小星星，伴随这声线~到我身边~~~​[星瞳·动态表情包_可爱猫猫]小星星们，2568，热烈招手不见不散！​[星瞳·动态表情包_星星期待]"
                    ],
                    "live_content": "暂无直播信息",
                    "relations": [
                        "星瞳邀请了@东爱璃Lovely 和 @雪糕cheese 一起直播游玩《胜利女神：新的希望》。"
                    ],
                    "rise_videos": [
                        [
                            "BV1c6jdzHE6x",
                            "N/A",
                            "打CALL教程❌体能测试✅",
                            15472,
                            3572
                        ]
                    ],
                    "tieba_topic": [
                        {
                            "topic": "新的一天，新的加9？",
                            "rank": 1,
                            "heat": 5,
                            "keywords": ["新的一天", "加9"],
                            "comments": ["妈妈妈", "急急急", "牛牛牛"]
                        }
                    ]
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID and room ID
        try:
            mid = get_user_mid(vtuber)
            room_id = get_user_room_id(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query recent activity information
        recent_info_obj = await query_recent_info(mid, room_id)

        if recent_info_obj is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No recent activity data found for Vup '{vtuber}'", 404)
            )

        # Convert VupRencentInfo object to dictionary
        recent_info_dict = {
            "time": recent_info_obj.time,
            "name": recent_info_obj.name,
            "follower_change": recent_info_obj.follower_change,
            "dahanghai_change": recent_info_obj.dahanghai_change,
            "video_content": recent_info_obj.video_content,
            "dynamic_content": recent_info_obj.dynamic_content,
            "live_content": recent_info_obj.live_content,
            "relations": recent_info_obj.relations,
            "rise_videos": recent_info_obj.rise_videos,
            "tieba_topic": recent_info_obj.tieba_topic
        }

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "room_id": room_id,
                "result": recent_info_dict,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Recent activity information retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get recent activity info for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve recent activity information", 500)
        )


@app.get("/info/recent/sumary")
async def read_summarise_rise_reason(recent: int = 30, vtuber: Optional[str] = "星瞳"):
    """
    Get AI-generated analysis of recent follower count changes and their reasons.

    Args:
        recent: Number of recent days to analyze (default: 30)
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with AI-generated follower change analysis

    Example:
        GET /info/recent/sumary/?recent=30&vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Follower change analysis retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "analysis_params": {
                    "recent_days": 30,
                    "target_date": "2025-08-04"
                },
                "result": "根据提供的信息，星瞳近期粉丝数减少的主要原因可能包括：\n1. 内容更新频率较低：最近的视频发布于2024-11-21，距离当前已有一段时间。长时间没有新视频内容可能导致部分粉丝失去兴趣。\n2. 视频播放量增长不足：虽然有几个视频的日播放量有所增长，但增长幅度不大...",
                "has_analysis": true,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if recent > 365:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' cannot exceed 365 days", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query AI-generated rise reason analysis
        target_date = datetime.now().strftime("%Y-%m-%d")
        rise_reason_analysis = await query_rise_reason_from_ai_gen_table_by_date(mid, target_date, recent)

        # Process the result
        has_analysis = rise_reason_analysis is not None and rise_reason_analysis.strip() != ""

        if not has_analysis:
            # Return empty analysis instead of 404 to maintain consistency
            rise_reason_analysis = "暂无AI分析数据，请稍后再试。"

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "analysis_params": {
                    "recent_days": recent,
                    "target_date": target_date
                },
                "result": rise_reason_analysis,
                "has_analysis": has_analysis,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Follower change analysis retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get follower change analysis for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve follower change analysis", 500)
        )


@app.get("/board/live")
async def read_all_live_status():
    """
    Get live status information for all VTubers.

    Returns:
        Standard API response with live status data for all VTubers

    Example:
        GET /board/live

        Response:
        {
            "code": 200,
            "message": "Live status information retrieved successfully",
            "data": {
                "list": [
                    {
                        "name": "星瞳",
                        "if_live": true,
                        "live_url": "https://live.bilibili.com/22625025",
                        "live_title": "今天也要元气满满！",
                        "live_cover": "https://i0.hdslb.com/bfs/live/cover.jpg",
                        "live_roomid": "22625025"
                    }
                ],
                "total_count": 1,
                "live_count": 1,
                "offline_count": 0,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    start_time = time.time()
    cache_status = "miss"

    try:
        # Query live status for all VTubers (with caching)
        live_status_data = await collect_all_live_status_stuff()

        if not live_status_data:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=create_error_response("No live status data available", 500)
            )

        # Optimized data processing using list comprehensions and built-in functions
        # This is much faster than the previous loop-based approach
        live_status_list = [
            {
                "name": status.get("name"),
                "if_live": status.get("if_live", False),
                "live_url": status.get("live_url"),
                "live_title": status.get("live_title"),
                "live_cover": status.get("live_cover"),
                "live_roomid": status.get("live_roomid")
            }
            for status in live_status_data if status
        ]

        # Efficient counting using sum() with generator expression
        live_count = sum(1 for status in live_status_list if status.get("if_live", False))
        offline_count = len(live_status_list) - live_count

        # Calculate processing time
        processing_time = time.time() - start_time

        # Determine cache status (if response was very fast, likely from cache)
        cache_status = "hit" if processing_time < 0.01 else "miss"

        response_data = {
            "list": live_status_list,
            "total_count": len(live_status_list),
            "live_count": live_count,
            "offline_count": offline_count,
            "retrieved_at": datetime.now().isoformat(),
            "cache_status": cache_status,
            "query_time": f"{processing_time:.3f}s"
        }

        logger.info(f"Live status endpoint completed in {processing_time:.3f}s "
                   f"({cache_status}, {len(live_status_list)} VTubers, {live_count} live)")

        return create_success_response(
            data=response_data,
            message="Live status information retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        processing_time = time.time() - start_time
        logger.error(f"Failed to get live status information after {processing_time:.3f}s: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve live status information", 500)
        )


@app.get("/board/stat")
async def read_all_dahanghai_and_follower_stuff(recent: int = 1):
    """
    Get follower and dahanghai statistics for all VTubers.

    Args:
        recent: Number of recent days to calculate changes (default: 1)

    Returns:
        Standard API response with follower and dahanghai statistics

    Example:
        GET /board/stat?recent=1

        Response:
        {
            "code": 200,
            "message": "Vup statistics retrieved successfully",
            "data": {
                "list": [
                    {
                        "name": "星瞳",
                        "dahanghai_num": 276,
                        "dahanghai_rise_num": -1,
                        "follower_num": 1234567,
                        "follower_rise_num": -44
                    }
                ],
                "query_params": {
                    "recent_days": 1
                },
                "total_count": 1,
                "summary": {
                    "total_followers": 1234567,
                    "total_dahanghai": 276,
                    "total_follower_change": -44,
                    "total_dahanghai_change": -1
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if recent > 30:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' cannot exceed 30 days", 400)
            )

        # Query statistics for all VTubers
        stats_data = await collect_all_dahanghai_and_follower_rise_num_stuff(recent)

        if stats_data is None:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=create_error_response("Failed to retrieve Vup statistics", 500)
            )

        # Extract statistics list from the returned data
        statistics_list = stats_data.get("data", [])

        # Calculate summary statistics
        total_followers = sum(stat.get("follower_num", 0) for stat in statistics_list)
        total_dahanghai = sum(stat.get("dahanghai_num", 0) for stat in statistics_list)
        total_follower_change = sum(stat.get("follower_rise_num", 0) for stat in statistics_list)
        total_dahanghai_change = sum(stat.get("dahanghai_rise_num", 0) for stat in statistics_list)

        return create_success_response(
            data={
                "list": statistics_list,
                "query_params": {
                    "recent_days": recent
                },
                "total_count": len(statistics_list),
                "summary": {
                    "total_followers": total_followers,
                    "total_dahanghai": total_dahanghai,
                    "total_follower_change": total_follower_change,
                    "total_dahanghai_change": total_dahanghai_change
                },
                "retrieved_at": datetime.now().isoformat()
            },
            message="Vup statistics retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get Vup statistics: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve Vup statistics", 500)
        )


@app.get("/board/info")
async def read_all_total_info():
    """
    Get basic information for all VTubers.

    Returns:
        Standard API response with Vup basic information

    Example:
        GET /board/info

        Response:
        {
            "code": 200,
            "message": "Vup information retrieved successfully",
            "data": {
                "list": [
                    {
                        "name": "星瞳",
                        "face": "https://i0.hdslb.com/bfs/face/avatar.jpg",
                        "sign": "今天也要元气满满！",
                        "birthday": "2020-05-01",
                        "cover_url": "https://i0.hdslb.com/bfs/space/cover.jpg"
                    }
                ],
                "total_count": 1,
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Query basic information for all VTubers
        info_data = await collect_whole_info_from_all_vtubers()

        if info_data is None:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=create_error_response("Failed to retrieve Vup information", 500)
            )

        # Extract Vup info list from the returned data
        vtuber_info_list = info_data.get("data", [])

        # Process and standardize the Vup information
        standardized_info = []
        for vtuber in vtuber_info_list:
            if vtuber:
                standardized_vtuber = {
                    "name": vtuber.get("name"),
                    "face": vtuber.get("face"),
                    "sign": vtuber.get("sign"),  # description/bio
                    "birthday": vtuber.get("birthday"),
                    "cover_url": vtuber.get("cover_url")
                }
                standardized_info.append(standardized_vtuber)

        return create_success_response(
            data={
                "list": standardized_info,
                "total_count": len(standardized_info),
                "retrieved_at": datetime.now().isoformat()
            },
            message="Vup information retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get Vup information: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve Vup information", 500)
        )


@app.get("/board/dahanghai/whole")
async def read_whole_flow_from_dahanghai_list(c: str, p: str):
    """
    Get overall dahanghai (guard) flow analysis between VTubers.

    Args:
        c: Current time string in YYYY-MM-DD format
        p: Previous time string in YYYY-MM-DD format

    Returns:
        Standard API response with dahanghai flow analysis

    Example:
        GET /board/dahanghai/whole/?c=2024-12-01&p=2024-11-27

        Response:
        {
            "code": 200,
            "message": "Dahanghai flow analysis retrieved successfully",
            "data": {
                "analysis_params": {
                    "current_date": "2024-12-01",
                    "previous_date": "2024-11-27",
                    "period_days": 4
                },
                "flow_analysis": {
                    "direct_loss": {},
                    "p2c_flow_between_anchors": {
                        "anchor1": {"anchor2": 1},
                        "anchor2": {"anchor1": 3}
                    },
                    "additional_users": {},
                    "c2p_flow_between_anchors": {
                        "anchor1": {"anchor2": 3},
                        "anchor2": {"anchor1": 1}
                    }
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not c or not c.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'c' (current date) cannot be empty", 400)
            )

        if not p or not p.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'p' (previous date) cannot be empty", 400)
            )

        # Validate date format and calculate period
        try:
            current_date = datetime.strptime(c, "%Y-%m-%d")
            previous_date = datetime.strptime(p, "%Y-%m-%d")
            period_days = (current_date - previous_date).days

            if period_days < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=create_error_response("Current date must be after or equal to previous date", 400)
                )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Query dahanghai flow analysis
        flow_data = await collect_whole_flow_from_all_vtubers(c, p)

        if flow_data is None:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=create_error_response("Failed to retrieve dahanghai flow analysis", 500)
            )

        return create_success_response(
            data={
                "analysis_params": {
                    "current_date": c,
                    "previous_date": p,
                    "period_days": period_days
                },
                "result": flow_data,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Dahanghai flow analysis retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get dahanghai flow analysis for dates {c} to {p}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve dahanghai flow analysis", 500)
        )


@app.get("/board/dahanghai/target")
async def read_target_flow_dahanghai_list(target: str, c: str, p: str, n: int = 5):
    """
    Get dahanghai (guard) flow analysis for a specific Vup.

    Args:
        target: Target Vup name
        c: Current time string in YYYY-MM-DD format
        p: Previous time string in YYYY-MM-DD format
        n: Number of top flows to return (default: 5)

    Returns:
        Standard API response with target Vup dahanghai flow analysis

    Example:
        GET /board/dahanghai/target/?target=星瞳&c=2024-12-01&p=2024-11-27&n=5

        Response:
        {
            "code": 200,
            "message": "Target Vup dahanghai flow analysis retrieved successfully",
            "data": {
                "target_vtuber": "星瞳",
                "analysis_params": {
                    "current_date": "2024-12-01",
                    "previous_date": "2024-11-27",
                    "top_n": 5,
                    "period_days": 4
                },
                "result": {
                    "in": {
                        "other_vtuber1": [
                            [12345, "username1", "avatar_url1"],
                            [67890, "username2", "avatar_url2"]
                        ]
                    },
                    "out": {
                        "other_vtuber2": [
                            [11111, "username3", "avatar_url3"]
                        ]
                    }
                },
                "summary": {
                    "total_inflow": 2,
                    "total_outflow": 1,
                    "net_flow": 1
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not target or not target.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'target' (Vup name) cannot be empty", 400)
            )

        if not c or not c.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'c' (current date) cannot be empty", 400)
            )

        if not p or not p.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'p' (previous date) cannot be empty", 400)
            )

        if n <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'n' must be a positive integer", 400)
            )

        if n > 50:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'n' cannot exceed 50", 400)
            )

        # Validate date format and calculate period
        try:
            current_date = datetime.strptime(c, "%Y-%m-%d")
            previous_date = datetime.strptime(p, "%Y-%m-%d")
            period_days = (current_date - previous_date).days

            if period_days < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=create_error_response("Current date must be after or equal to previous date", 400)
                )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Query target Vup dahanghai flow analysis
        flow_data = await collect_target_flow_with_target_vtuber(target, c, p, n)

        if flow_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No dahanghai flow data found for Vup '{target}' in the specified period", 404)
            )

        # Calculate summary statistics
        inflow_data = flow_data.get("in", {})
        outflow_data = flow_data.get("out", {})

        total_inflow = sum(len(users) for users in inflow_data.values())
        total_outflow = sum(len(users) for users in outflow_data.values())
        net_flow = total_inflow - total_outflow

        return create_success_response(
            data={
                "target_vtuber": target,
                "analysis_params": {
                    "current_date": c,
                    "previous_date": p,
                    "top_n": n,
                    "period_days": period_days
                },
                "result": flow_data,
                "summary": {
                    "total_inflow": total_inflow,
                    "total_outflow": total_outflow,
                    "net_flow": net_flow
                },
                "retrieved_at": datetime.now().isoformat()
            },
            message="Target Vup dahanghai flow analysis retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get dahanghai flow analysis for Vup {target} from {p} to {c}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve target Vup dahanghai flow analysis", 500)
        )


@app.get("/board/dahanghai/target_period")
async def read_target_flow_dahanghai_list_by_period(
    target: str,
    start_time_str1: str,
    end_time_str1: str,
    start_time_str2: str,
    end_time_str2: str,
    n: int = 5,
):
    """
    Get dahanghai (guard) flow analysis for a specific Vup between two time periods.

    Args:
        target: Target Vup name
        start_time_str1: Start date of first period in YYYY-MM-DD format
        end_time_str1: End date of first period in YYYY-MM-DD format
        start_time_str2: Start date of second period in YYYY-MM-DD format
        end_time_str2: End date of second period in YYYY-MM-DD format
        n: Number of top flows to return (default: 5)

    Returns:
        Standard API response with target Vup dahanghai flow analysis between periods

    Example:
        GET /board/dahanghai/target_period/?target=星瞳&start_time_str1=2024-11-01&end_time_str1=2024-11-30&start_time_str2=2024-12-01&end_time_str2=2024-12-31&n=5

        Response:
        {
            "code": 200,
            "message": "Target Vup period dahanghai flow analysis retrieved successfully",
            "data": {
                "target_vtuber": "星瞳",
                "analysis_params": {
                    "period1": {
                        "start_date": "2024-11-01",
                        "end_date": "2024-11-30",
                        "duration_days": 29
                    },
                    "period2": {
                        "start_date": "2024-12-01",
                        "end_date": "2024-12-31",
                        "duration_days": 30
                    },
                    "top_n": 5
                },
                "result": {
                    "in": {
                        "other_vtuber1": [
                            [12345, "username1", "avatar_url1"],
                            [67890, "username2", "avatar_url2"]
                        ]
                    },
                    "out": {
                        "other_vtuber2": [
                            [11111, "username3", "avatar_url3"]
                        ]
                    }
                },
                "summary": {
                    "total_inflow": 2,
                    "total_outflow": 1,
                    "net_flow": 1
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not target or not target.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'target' (Vup name) cannot be empty", 400)
            )

        required_dates = {
            'start_time_str1': start_time_str1,
            'end_time_str1': end_time_str1,
            'start_time_str2': start_time_str2,
            'end_time_str2': end_time_str2
        }

        for param_name, param_value in required_dates.items():
            if not param_value or not param_value.strip():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=create_error_response(f"Parameter '{param_name}' cannot be empty", 400)
                )

        if n <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'n' must be a positive integer", 400)
            )

        if n > 50:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'n' cannot exceed 50", 400)
            )

        # Validate date format and calculate periods
        try:
            period1_start = datetime.strptime(start_time_str1, "%Y-%m-%d")
            period1_end = datetime.strptime(end_time_str1, "%Y-%m-%d")
            period2_start = datetime.strptime(start_time_str2, "%Y-%m-%d")
            period2_end = datetime.strptime(end_time_str2, "%Y-%m-%d")

            period1_duration = (period1_end - period1_start).days
            period2_duration = (period2_end - period2_start).days

            if period1_duration < 0 or period2_duration < 0:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=create_error_response("End date must be after or equal to start date for both periods", 400)
                )
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Query target Vup dahanghai flow analysis by period
        flow_data = await collect_target_flow_with_target_vtuber_by_period(
            target, start_time_str1, end_time_str1, start_time_str2, end_time_str2, n
        )

        if flow_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"No dahanghai flow data found for Vup '{target}' in the specified periods", 404)
            )

        # Calculate summary statistics
        inflow_data = flow_data.get("in", {})
        outflow_data = flow_data.get("out", {})

        total_inflow = sum(len(users) for users in inflow_data.values())
        total_outflow = sum(len(users) for users in outflow_data.values())
        net_flow = total_inflow - total_outflow

        return create_success_response(
            data={
                "target_vtuber": target,
                "analysis_params": {
                    "period1": {
                        "start_date": start_time_str1,
                        "end_date": end_time_str1,
                        "duration_days": period1_duration
                    },
                    "period2": {
                        "start_date": start_time_str2,
                        "end_date": end_time_str2,
                        "duration_days": period2_duration
                    },
                    "top_n": n
                },
                "result": flow_data,
                "summary": {
                    "total_inflow": total_inflow,
                    "total_outflow": total_outflow,
                    "net_flow": net_flow
                },
                "retrieved_at": datetime.now().isoformat()
            },
            message="Target Vup period dahanghai flow analysis retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get period dahanghai flow analysis for Vup {target}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve target Vup period dahanghai flow analysis", 500)
        )


@app.get("/follower/new_list")
async def read_follower_new_list(recent: int = 7, vtuber: Optional[str] = "星瞳"):
    """
    Get new followers list for a Vup within recent days.

    Args:
        recent: Number of recent days to query (default: 7)
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with new followers list

    Example:
        GET /follower/new_list?recent=7&vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "New followers list retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "recent_days": 7,
                "list": [
                    {
                        "mid": 386169396,
                        "attribute": 0,
                        "mtime": 1734873176,
                        "tag": null,
                        "special": 0,
                        "contract_info": {},
                        "uname": "星辰葱姜",
                        "face": "http://i0.hdslb.com/bfs/face/8dfdfb0e8ae10e88161d918f5926ccb3bc731612.jpg",
                        "sign": "你不再武器，而是人如其名的人",
                        "face_nft": 0,
                        "official_verify": {
                            "type": -1,
                            "desc": ""
                        },
                        "vip": {
                            "vipType": 1,
                            "vipDueDate": 1711296000000,
                            "dueRemark": "",
                            "accessStatus": 0,
                            "vipStatus": 0,
                            "vipStatusWarn": "",
                            "themeType": 0,
                            "label": {
                                "path": "",
                                "text": "",
                                "label_theme": "",
                                "text_color": "",
                                "bg_style": 0,
                                "bg_color": "",
                                "border_color": ""
                            },
                            "avatar_subscript": 0,
                            "nickname_color": "",
                            "avatar_subscript_url": ""
                        },
                        "name_render": {},
                        "nft_icon": "",
                        "rec_reason": "",
                        "track_id": "",
                        "follow_time": ""
                    }
                ],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query new followers list
        cur_datetime = datetime.now().strftime("%Y-%m-%d")
        followers_data = await query_followers_list(mid, cur_datetime, recent)

        if followers_data is None:
            followers_data = []

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "recent_days": recent,
                "list": followers_data,
                "retrieved_at": datetime.now().isoformat()
            },
            message="New followers list retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get new followers list for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve new followers list", 500)
        )


@app.get("/follower/review_list")
async def read_follower_review_list(recent: int = 7, vtuber: Optional[str] = "星瞳"):
    """
    Get new followers' live stream viewing status list.

    Args:
        recent: Number of recent days to query (default: 7)
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with followers' viewing status list

    Example:
        GET /follower/review_list?recent=7&vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Followers review list retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "recent_days": 7,
                "list": [
                    {
                        "id": 1,
                        "vup_mid": "401315430",
                        "vup_name": "星瞳",
                        "date": "2025-05-19",
                        "recent_days": 7,
                        "follower_mid": "1342882756",
                        "follower_name": "一只峨眉山野猴",
                        "has_watched": false
                    },
                    {
                        "id": 2,
                        "vup_mid": "401315430",
                        "vup_name": "星瞳",
                        "date": "2025-05-19",
                        "recent_days": 7,
                        "follower_mid": "3546611519064497",
                        "follower_name": "浅默淡殇乀",
                        "has_watched": false
                    }
                ],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query followers review list
        cur_time = datetime.now().strftime("%Y-%m-%d")
        review_data = await query_followers_review_list(mid, cur_time, recent)

        if review_data is None:
            review_data = []

        # Convert array data to structured format
        structured_data = []
        for item in review_data:
            if isinstance(item, (list, tuple)) and len(item) >= 8:
                structured_data.append({
                    "id": item[0],
                    "vup_mid": item[1],
                    "vup_name": item[2],
                    "date": item[3],
                    "recent_days": item[4],
                    "follower_mid": item[5],
                    "follower_name": item[6],
                    "has_watched": item[7]
                })

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "recent_days": recent,
                "list": structured_data,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Followers review list retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get followers review list for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve followers review list", 500)
        )


@app.get("/follower/review_rate")
async def read_follower_review_rate(
    recent: Optional[int] = 7, vtuber: Optional[str] = "星瞳"
):
    """
    Get sticky followers rate (percentage of new followers who watch live streams).

    Args:
        recent: Number of recent days to query (default: 7)
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with sticky followers rate

    Example:
        GET /follower/review_rate?recent=7&vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Sticky followers rate retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "mid": "401315430",
                "recent_days": 7,
                "result": "2%",
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if recent is not None and recent <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'recent' must be a positive integer", 400)
            )

        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup MID
        try:
            mid = get_user_mid(vtuber)
        except Exception as e:
            logger.error(f"Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Vup '{vtuber}' not found", 404)
            )

        # Query followers review rate
        cur_time = datetime.now().strftime("%Y-%m-%d")
        rate_data = await query_followers_review_rate(mid, cur_time, recent)

        if rate_data is None:
            rate_data = "0%"

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "mid": mid,
                "recent_days": recent,
                "result": rate_data,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Sticky followers rate retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get followers review rate for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve sticky followers rate", 500)
        )


@app.get("/live/sessions")
async def read_live_sessions(vtuber: Optional[str] = "星瞳"):
    """
    Get all live streaming sessions for a Vup.

    Args:
        vtuber: Vup name (default: "星瞳")

    Returns:
        Standard API response with live sessions list

    Example:
        GET /live/sessions?vtuber=星瞳

        Response:
        {
            "code": 200,
            "message": "Live sessions retrieved successfully",
            "data": {
                "vtuber_name": "星瞳",
                "room_id": "22886883",
                "list": [
                    {
                        "liveId": "test_live_id2",
                        "isFinish": false,
                        "isFull": false,
                        "parentArea": "Parent Area",
                        "area": "Area",
                        "coverUrl": "Test Cover",
                        "danmakusCount": 300,
                        "startDate": "2023-01-01 12:00:00",
                        "stopDate": "2023-01-01 14:00:00",
                        "title": "Test Title",
                        "totalIncome": 1000.0,
                        "watchCount": 500,
                        "likeCount": 200,
                        "payCount": 10,
                        "interactionCount": 50,
                        "onlineRank": 100
                    }
                ],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not vtuber or not vtuber.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'vtuber' cannot be empty", 400)
            )

        # Get Vup room ID
        try:
            room_id = get_user_room_id(vtuber)
        except Exception as e:
            logger.error(f"Room ID for Vup {vtuber} not found: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Room ID for Vup '{vtuber}' not found", 404)
            )

        # Query live sessions
        sessions_data = await query_live_info_with_room_id(room_id)

        if sessions_data is None:
            sessions_data = []

        return create_success_response(
            data={
                "vtuber_name": vtuber,
                "room_id": room_id,
                "list": sessions_data,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Live sessions retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get live sessions for Vup {vtuber}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve live sessions", 500)
        )


@app.get("/live/one_session")
async def read_live_one_session(liveId: str):
    """
    Get complete information for a specific live session by live ID.

    Args:
        liveId: Live session ID

    Returns:
        Standard API response with live session details

    Example:
        GET /live/one_session?liveId=18e1008c-2737-42b1-a1b7-d74c59eecb0c

        Response:
        {
            "code": 200,
            "message": "Live session retrieved successfully",
            "data": {
                "live_id": "18e1008c-2737-42b1-a1b7-d74c59eecb0c",
                "result": {
                    "liveId": "18e1008c-2737-42b1-a1b7-d74c59eecb0c",
                    "isFinish": false,
                    "isFull": false,
                    "parentArea": "Parent Area",
                    "area": "Area",
                    "coverUrl": "Test Cover",
                    "danmakusCount": 300,
                    "startDate": "2023-01-01 12:00:00",
                    "stopDate": "2023-01-01 14:00:00",
                    "title": "Test Title",
                    "totalIncome": 1000.0,
                    "watchCount": 500,
                    "likeCount": 200,
                    "payCount": 10,
                    "interactionCount": 50,
                    "onlineRank": 100
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not liveId or not liveId.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'liveId' cannot be empty", 400)
            )

        # Query live session information
        session_data = await query_whole_live_info_with_live_id(liveId)

        if session_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Live session with ID '{liveId}' not found", 404)
            )

        return create_success_response(
            data={
                "live_id": liveId,
                "result": session_data,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Live session retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get live session with ID {liveId}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve live session", 500)
        )


@app.get("/live/one_session/minute")
async def read_live_one_session_with_minutes(liveId: str, interval: Optional[int] = 10):
    """
    Get complete information for a specific live session including minute-by-minute data.

    Args:
        liveId: Live session ID
        interval: Data sampling interval in minutes (default: 10)

    Returns:
        Standard API response with live session details and minute-by-minute data

    Example:
        GET /live/one_session/minute?liveId=18e1008c-2737-42b1-a1b7-d74c59eecb0c&interval=10

        Response:
        {
            "code": 200,
            "message": "Live session with minute data retrieved successfully",
            "data": {
                "live_id": "18e1008c-2737-42b1-a1b7-d74c59eecb0c",
                "interval_minutes": 10,
                "result": {
                    "liveId": "18e1008c-2737-42b1-a1b7-d74c59eecb0c",
                    "isFinish": false,
                    "isFull": false,
                    "parentArea": "Parent Area",
                    "area": "Area",
                    "coverUrl": "Test Cover",
                    "danmakusCount": 300,
                    "startDate": "2023-01-01 12:00:00",
                    "stopDate": "2023-01-01 14:00:00",
                    "title": "Test Title",
                    "totalIncome": 1000.0,
                    "watchCount": 500,
                    "likeCount": 200,
                    "payCount": 10,
                    "interactionCount": 50,
                    "onlineRank": 100
                },
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not liveId or not liveId.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'liveId' cannot be empty", 400)
            )

        if interval is not None and interval <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'interval' must be a positive integer", 400)
            )

        # Query live session information with minute data
        session_data = await query_minutes_live_info_with_live_id(liveId, interval)

        if session_data is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response(f"Live session with ID '{liveId}' not found", 404)
            )

        return create_success_response(
            data={
                "live_id": liveId,
                "interval_minutes": interval,
                "result": session_data,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Live session with minute data retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get live session with minute data for ID {liveId}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve live session with minute data", 500)
        )


# --- Creator Info API Endpoints ---

@app.get("/creator/overview/stat/by-date-range")
async def read_creator_overview_stat_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Query creator overview statistics by date range.

    Args:
        uid: User ID
        start_date: Start date in YYYY-MM-DD or YYYY-MM-DD HH:MM:SS format
        end_date: End date in YYYY-MM-DD or YYYY-MM-DD HH:MM:SS format
        limit: Maximum number of records to return (default: 1000)
        offset: Pagination offset (default: 0)

    Returns:
        Standard API response with creator overview statistics

    Example:
        GET /creator/overview/stat/by-date-range?uid=401315430&start_date=2025-01-01&end_date=2025-01-31

        Response:
        {
            "code": 200,
            "message": "Creator overview statistics retrieved successfully",
            "data": {
                "uid": "401315430",
                "date_range": {
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-31"
                },
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 31
                },
                "list": [...],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not uid or not uid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'uid' cannot be empty", 400)
            )

        if not start_date or not start_date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'start_date' cannot be empty", 400)
            )

        if not end_date or not end_date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'end_date' cannot be empty", 400)
            )

        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'limit' must be a positive integer", 400)
            )

        if offset < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'offset' must be non-negative", 400)
            )

        # Query creator overview statistics
        results, total_count = await query_overview_stat_by_date_range(
            uid, start_date, end_date, limit, offset
        )

        return create_success_response(
            data={
                "uid": uid,
                "date_range": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Creator overview statistics retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get creator overview statistics by date range for uid {uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve creator overview statistics", 500)
        )


@app.get("/creator/overview/stat/by-date")
async def read_creator_overview_stat_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Query creator overview statistics by specific date.

    Args:
        uid: User ID
        date: Query date in YYYY-MM-DD format
        limit: Maximum number of records to return (default: 1000)
        offset: Pagination offset (default: 0)

    Returns:
        Standard API response with creator overview statistics

    Example:
        GET /creator/overview/stat/by-date?uid=401315430&date=2025-01-15

        Response:
        {
            "code": 200,
            "message": "Creator overview statistics retrieved successfully",
            "data": {
                "uid": "401315430",
                "date": "2025-01-15",
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 1
                },
                "list": [...],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not uid or not uid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'uid' cannot be empty", 400)
            )

        if not date or not date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'date' cannot be empty", 400)
            )

        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'limit' must be a positive integer", 400)
            )

        if offset < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Parameter 'offset' must be non-negative", 400)
            )

        # Validate date format
        try:
            datetime.strptime(date, "%Y-%m-%d")
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Invalid date format. Use YYYY-MM-DD format", 400)
            )

        # Query creator overview statistics
        results, total_count = await query_overview_stat_by_date(
            uid, date, limit, offset
        )

        return create_success_response(
            data={
                "uid": uid,
                "date": date,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Creator overview statistics retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get creator overview statistics by date for uid {uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve creator overview statistics", 500)
        )

@app.get("/creator/attention/analyze/by-date-range")
async def read_creator_attention_analyze_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Retrieve creator's attention analysis data within a date range.
    
    Args:
        uid (str): Creator's unique identifier
        start_date (str): Start date in format YYYY-MM-DD or YYYY-MM-DD HH:MM:SS
        end_date (str): End date in format YYYY-MM-DD or YYYY-MM-DD HH:MM:SS
        limit (int, optional): Maximum number of records to return. Defaults to 1000.
        offset (int, optional): Number of records to skip. Defaults to 0.

    Returns:
        JSONResponse: A JSON object containing:
            - success (bool): Operation success status
            - data (list): List of attention analysis records
            - total (int): Total number of records
            - message (str): Success message

    Raises:
        HTTPException: 
            - 400: Invalid parameters provided
            - 404: No data found for the specified criteria
            - 500: Server error during data retrieval
    """
    try:
        # Validate input parameters
        if not uid or not start_date or not end_date:
            raise ValueError("Missing required parameters: uid, start_date, and end_date are required")

        # Validate date formats
        try:
            datetime.strptime(start_date.split()[0], '%Y-%m-%d')
            datetime.strptime(end_date.split()[0], '%Y-%m-%d')
        except ValueError:
            raise ValueError("Invalid date format. Use YYYY-MM-DD or YYYY-MM-DD HH:MM:SS")

        results, total_count = await query_attention_analyze_by_date_range(
            uid, start_date, end_date, limit, offset
        )

        if not results:
            return create_success_response(
                data=[],
                total=0,
                message="No attention analysis data found for the specified date range"
            )

        return create_success_response(
            data=results,
            total=total_count,
            message="Successfully retrieved attention analysis data"
        )

    except ValueError as ve:
        logger.warning(f"Invalid parameters in attention analysis request: {ve}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(ve)
        )
    except Exception as e:
        logger.error(f"Error fetching creator attention analyze by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch attention analyze data by date range: {str(e)}"
        )


@app.get("/creator/attention/analyze/by-date")
async def read_creator_attention_analyze_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Retrieve creator's attention analysis data for a specific date.
    
    Args:
        uid (str): Creator's unique identifier
        date (str): Target date in format YYYY-MM-DD
        limit (int, optional): Maximum number of records to return. Defaults to 1000.
        offset (int, optional): Number of records to skip. Defaults to 0.

    Returns:
        JSONResponse: A JSON object containing:
            - success (bool): Operation success status
            - data (list): List of attention analysis records
            - total (int): Total number of records
            - message (str): Success message

    Raises:
        HTTPException: 
            - 400: Invalid parameters provided
            - 404: No data found for the specified date
            - 500: Server error during data retrieval
    """
    try:
        # Validate input parameters
        if not uid or not date:
            raise ValueError("Missing required parameters: uid and date are required")

        # Validate date format
        try:
            datetime.strptime(date, '%Y-%m-%d')
        except ValueError:
            raise ValueError("Invalid date format. Use YYYY-MM-DD")

        results, total_count = await query_attention_analyze_by_date(
            uid, date, limit, offset
        )

        if not results:
            return create_success_response(
                data=[],
                total=0,
                message=f"No attention analysis data found for date {date}"
            )

        return create_success_response(
            data=results,
            total=total_count,
            message="Successfully retrieved attention analysis data"
        )

    except ValueError as ve:
        logger.warning(f"Invalid parameters in attention analysis request: {ve}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(ve)
        )
    except Exception as e:
        logger.error(f"Error fetching creator attention analyze by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch attention analyze data by date: {str(e)}"
        )


@app.get("/creator/archive/analyze/by-date-range")
async def read_creator_archive_analyze_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    period: Optional[int] = None,
    limit: int = 1000,
    offset: int = 0
):
    """
    Retrieve creator's archive (video) analysis data within a date range.
    
    Args:
        uid (str): Creator's unique identifier
        start_date (str): Start date in format YYYY-MM-DD or YYYY-MM-DD HH:MM:SS
        end_date (str): End date in format YYYY-MM-DD or YYYY-MM-DD HH:MM:SS
        period (int, optional): Analysis period filter. Defaults to None.
        limit (int, optional): Maximum number of records to return. Defaults to 1000.
        offset (int, optional): Number of records to skip. Defaults to 0.

    Returns:
        JSONResponse: A JSON object containing:
            - success (bool): Operation success status
            - data (list): List of archive analysis records
            - total (int): Total number of records
            - message (str): Success message

    Raises:
        HTTPException: 
            - 400: Invalid parameters provided
            - 404: No data found for the specified criteria
            - 500: Server error during data retrieval
    """
    try:
        # Validate input parameters
        if not uid or not start_date or not end_date:
            raise ValueError("Missing required parameters: uid, start_date, and end_date are required")

        # Validate date formats
        try:
            datetime.strptime(start_date.split()[0], '%Y-%m-%d')
            datetime.strptime(end_date.split()[0], '%Y-%m-%d')
        except ValueError:
            raise ValueError("Invalid date format. Use YYYY-MM-DD or YYYY-MM-DD HH:MM:SS")

        # Validate period if provided
        if period is not None and period <= 0:
            raise ValueError("Period must be a positive integer")

        results, total_count = await query_archive_analyze_by_date_range(
            uid, start_date, end_date, period, limit, offset
        )

        if not results:
            return create_success_response(
                data=[],
                total=0,
                message="No archive analysis data found for the specified date range"
            )

        return create_success_response(
            data=results,
            total=total_count,
            message="Successfully retrieved archive analysis data"
        )

    except ValueError as ve:
        logger.warning(f"Invalid parameters in archive analysis request: {ve}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(ve)
        )
    except Exception as e:
        logger.error(f"Error fetching creator archive analyze by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch archive analyze data by date range: {str(e)}"
        )


@app.get("/creator/archive/analyze/by-date")
async def read_creator_archive_analyze_by_date(
    uid: str,
    date: str,
    period: Optional[int] = None,
    limit: int = 1000,
    offset: int = 0
):
    """
    Retrieve creator's archive (video) analysis data for a specific date.
    
    Args:
        uid (str): Creator's unique identifier
        date (str): Target date in format YYYY-MM-DD
        period (int, optional): Analysis period filter. Defaults to None.
        limit (int, optional): Maximum number of records to return. Defaults to 1000.
        offset (int, optional): Number of records to skip. Defaults to 0.

    Returns:
        JSONResponse: A JSON object containing:
            - success (bool): Operation success status
            - data (list): List of archive analysis records
            - total (int): Total number of records
            - message (str): Success message

    Raises:
        HTTPException: 
            - 400: Invalid parameters provided
            - 404: No data found for the specified date
            - 500: Server error during data retrieval
    """
    try:
        # Validate input parameters
        if not uid or not date:
            raise ValueError("Missing required parameters: uid and date are required")

        # Validate date format
        try:
            datetime.strptime(date, '%Y-%m-%d')
        except ValueError:
            raise ValueError("Invalid date format. Use YYYY-MM-DD")

        # Validate period if provided
        if period is not None and period <= 0:
            raise ValueError("Period must be a positive integer")

        results, total_count = await query_archive_analyze_by_date(
            uid, date, period, limit, offset
        )

        if not results:
            return create_success_response(
                data=[],
                total=0,
                message=f"No archive analysis data found for date {date}"
            )

        return create_success_response(
            data=results,
            total=total_count,
            message="Successfully retrieved archive analysis data"
        )

    except ValueError as ve:
        logger.warning(f"Invalid parameters in archive analysis request: {ve}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(ve)
        )
    except Exception as e:
        logger.error(f"Error fetching creator archive analyze by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch archive analyze data by date: {str(e)}"
        )


@app.get("/creator/fan/graph/by-date-range")
async def read_creator_fan_graph_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Retrieve creator's fan graph data within a date range.
    
    Args:
        uid (str): Creator's unique identifier
        start_date (str): Start date in format YYYY-MM-DD or YYYY-MM-DD HH:MM:SS
        end_date (str): End date in format YYYY-MM-DD or YYYY-MM-DD HH:MM:SS
        limit (int, optional): Maximum number of records to return. Defaults to 1000.
        offset (int, optional): Number of records to skip. Defaults to 0.

    Returns:
        JSONResponse: A JSON object containing:
            - success (bool): Operation success status
            - data (dict): Fan graph data with metadata
                - uid (str): Creator's ID
                - date_range (dict): Start and end dates
                - pagination (dict): Limit, offset and total count
                - graph_data (list): List of fan graph records
                - retrieved_at (str): Timestamp of data retrieval
            - message (str): Success message

    Raises:
        HTTPException: 
            - 400: Invalid parameters provided
            - 404: No data found for the specified criteria
            - 500: Server error during data retrieval

    Example:
        GET /creator/fan/graph/by-date-range?uid=401315430&start_date=2025-01-01&end_date=2025-01-31

        Response:
        {
            "success": true,
            "data": {
                "uid": "401315430",
                "date_range": {
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-31"
                },
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 31
                },
                "graph_data": [...],
                "retrieved_at": "2025-08-06T12:00:00"
            },
            "message": "Fan graph data retrieved successfully"
        }
    """
    try:
        # Validate input parameters
        if not uid or not uid.strip():
            raise ValueError("User ID is required")

        if not start_date or not start_date.strip():
            raise ValueError("Start date is required")

        if not end_date or not end_date.strip():
            raise ValueError("End date is required")

        # Validate date formats
        try:
            datetime.strptime(start_date.split()[0], '%Y-%m-%d')
            datetime.strptime(end_date.split()[0], '%Y-%m-%d')
        except ValueError:
            raise ValueError("Invalid date format. Use YYYY-MM-DD or YYYY-MM-DD HH:MM:SS")

        # Validate pagination parameters
        if limit <= 0:
            raise ValueError("Limit must be greater than 0")
        if offset < 0:
            raise ValueError("Offset must be non-negative")

        results, total_count = await query_fan_graph_by_date_range(
            uid, start_date, end_date, limit, offset
        )

        if not results:
            return create_success_response(
                data={
                    "uid": uid,
                    "date_range": {
                        "start_date": start_date,
                        "end_date": end_date
                    },
                    "pagination": {
                        "limit": limit,
                        "offset": offset,
                        "total": 0
                    },
                    "list": [],
                    "retrieved_at": datetime.now().isoformat()
                },
                message="No fan graph data found for the specified date range"
            )

        return create_success_response(
            data={
                "uid": uid,
                "date_range": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Fan graph data retrieved successfully"
        )

    except ValueError as ve:
        logger.warning(f"Invalid parameters in fan graph request: {ve}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(ve)
        )
    except Exception as e:
        logger.error(f"Error fetching creator fan graph by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch fan graph data by date range: {str(e)}"
        )


@app.get("/creator/fan/graph/by-date")
async def read_creator_fan_graph_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Retrieve creator's fan graph data for a specific date.
    
    Args:
        uid (str): Creator's unique identifier
        date (str): Target date in format YYYY-MM-DD
        limit (int, optional): Maximum number of records to return. Defaults to 1000.
        offset (int, optional): Number of records to skip. Defaults to 0.

    Returns:
        JSONResponse: A JSON object containing:
            - success (bool): Operation success status
            - data (dict): Fan graph data with metadata
                - uid (str): Creator's ID
                - date (str): Query date
                - pagination (dict): Limit, offset and total count
                - graph_data (list): List of fan graph records
                - retrieved_at (str): Timestamp of data retrieval
            - message (str): Success message

    Raises:
        HTTPException: 
            - 400: Invalid parameters provided
            - 404: No data found for the specified date
            - 500: Server error during data retrieval

    Example:
        GET /creator/fan/graph/by-date?uid=401315430&date=2025-01-15

        Response:
        {
            "success": true,
            "data": {
                "uid": "401315430",
                "date": "2025-01-15",
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 24
                },
                "graph_data": [...],
                "retrieved_at": "2025-08-06T12:00:00"
            },
            "message": "Fan graph data retrieved successfully"
        }
    """
    try:
        # Validate input parameters
        if not uid or not uid.strip():
            raise ValueError("User ID is required")

        if not date or not date.strip():
            raise ValueError("Date is required")

        # Validate date format
        try:
            datetime.strptime(date, '%Y-%m-%d')
        except ValueError:
            raise ValueError("Invalid date format. Use YYYY-MM-DD")

        # Validate pagination parameters
        if limit <= 0:
            raise ValueError("Limit must be greater than 0")
        if offset < 0:
            raise ValueError("Offset must be non-negative")

        results, total_count = await query_fan_graph_by_date(
            uid, date, limit, offset
        )

        if not results:
            return create_success_response(
                data={
                    "uid": uid,
                    "date": date,
                    "pagination": {
                        "limit": limit,
                        "offset": offset,
                        "total": 0
                    },
                    "list": [],
                    "retrieved_at": datetime.now().isoformat()
                },
                message=f"No fan graph data found for date {date}"
            )

        return create_success_response(
            data={
                "uid": uid,
                "date": date,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Fan graph data retrieved successfully"
        )

    except ValueError as ve:
        logger.warning(f"Invalid parameters in fan graph request: {ve}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(ve)
        )
    except Exception as e:
        logger.error(f"Error fetching creator fan graph by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch fan graph data by date: {str(e)}"
        )

@app.get("/creator/fan/overview/by-date-range")
async def read_creator_fan_overview_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Retrieve creator's fan overview data within a date range.
    
    Args:
        uid (str): Creator's unique identifier
        start_date (str): Start date in format YYYY-MM-DD or YYYY-MM-DD HH:MM:SS
        end_date (str): End date in format YYYY-MM-DD or YYYY-MM-DD HH:MM:SS
        limit (int, optional): Maximum number of records to return. Defaults to 1000.
        offset (int, optional): Number of records to skip. Defaults to 0.

    Returns:
        JSONResponse: A JSON object containing:
            - success (bool): Operation success status
            - data (dict): Fan overview data with metadata
                - uid (str): Creator's ID
                - date_range (dict): Start and end dates
                - pagination (dict): Limit, offset and total count
                - overview_data (list): List of fan overview records
                - retrieved_at (str): Timestamp of data retrieval
            - message (str): Success message

    Raises:
        HTTPException: 
            - 400: Invalid parameters provided
            - 404: No data found for the specified criteria
            - 500: Server error during data retrieval

    Example:
        GET /creator/fan/overview/by-date-range?uid=401315430&start_date=2025-01-01&end_date=2025-01-31

        Response:
        {
            "success": true,
            "data": {
                "uid": "401315430",
                "date_range": {
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-31"
                },
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 31
                },
                "list": [...],
                "retrieved_at": "2025-08-06T12:00:00"
            },
            "message": "Fan overview data retrieved successfully"
        }
    """
    try:
        # Validate input parameters
        if not uid or not uid.strip():
            raise ValueError("User ID is required")

        if not start_date or not start_date.strip():
            raise ValueError("Start date is required")

        if not end_date or not end_date.strip():
            raise ValueError("End date is required")

        # Validate date formats
        try:
            datetime.strptime(start_date.split()[0], '%Y-%m-%d')
            datetime.strptime(end_date.split()[0], '%Y-%m-%d')
        except ValueError:
            raise ValueError("Invalid date format. Use YYYY-MM-DD or YYYY-MM-DD HH:MM:SS")

        # Validate pagination parameters
        if limit <= 0:
            raise ValueError("Limit must be greater than 0")
        if offset < 0:
            raise ValueError("Offset must be non-negative")

        results, total_count = await query_fan_overview_by_date_range(
            uid, start_date, end_date, limit, offset
        )

        if not results:
            return create_success_response(
                data={
                    "uid": uid,
                    "date_range": {
                        "start_date": start_date,
                        "end_date": end_date
                    },
                    "pagination": {
                        "limit": limit,
                        "offset": offset,
                        "total": 0
                    },
                    "list": [],
                    "retrieved_at": datetime.now().isoformat()
                },
                message="No fan overview data found for the specified date range"
            )

        return create_success_response(
            data={
                "uid": uid,
                "date_range": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Fan overview data retrieved successfully"
        )

    except ValueError as ve:
        logger.warning(f"Invalid parameters in fan overview request: {ve}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(ve)
        )
    except Exception as e:
        logger.error(f"Error fetching creator fan overview by date range: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch fan overview data by date range: {str(e)}"
        )


@app.get("/creator/fan/overview/by-date")
async def read_creator_fan_overview_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Retrieve creator's fan overview data for a specific date.
    
    Args:
        uid (str): Creator's unique identifier
        date (str): Target date in format YYYY-MM-DD
        limit (int, optional): Maximum number of records to return. Defaults to 1000.
        offset (int, optional): Number of records to skip. Defaults to 0.

    Returns:
        JSONResponse: A JSON object containing:
            - success (bool): Operation success status
            - data (dict): Fan overview data with metadata
                - uid (str): Creator's ID
                - date (str): Query date
                - pagination (dict): Limit, offset and total count
                - overview_data (list): List of fan overview records
                - retrieved_at (str): Timestamp of data retrieval
            - message (str): Success message

    Raises:
        HTTPException: 
            - 400: Invalid parameters provided
            - 404: No data found for the specified date
            - 500: Server error during data retrieval

    Example:
        GET /creator/fan/overview/by-date?uid=401315430&date=2025-01-15

        Response:
        {
            "success": true,
            "data": {
                "uid": "401315430",
                "date": "2025-01-15",
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 24
                },
                "list": [...],
                "retrieved_at": "2025-08-06T12:00:00"
            },
            "message": "Fan overview data retrieved successfully"
        }
    """
    try:
        # Validate input parameters
        if not uid or not uid.strip():
            raise ValueError("User ID is required")

        if not date or not date.strip():
            raise ValueError("Date is required")

        # Validate date format
        try:
            datetime.strptime(date, '%Y-%m-%d')
        except ValueError:
            raise ValueError("Invalid date format. Use YYYY-MM-DD")

        # Validate pagination parameters
        if limit <= 0:
            raise ValueError("Limit must be greater than 0")
        if offset < 0:
            raise ValueError("Offset must be non-negative")

        results, total_count = await query_fan_overview_by_date(
            uid, date, limit, offset
        )

        if not results:
            return create_success_response(
                data={
                    "uid": uid,
                    "date": date,
                    "pagination": {
                        "limit": limit,
                        "offset": offset,
                        "total": 0
                    },
                    "list": [],
                    "retrieved_at": datetime.now().isoformat()
                },
                message=f"No fan overview data found for date {date}"
            )

        return create_success_response(
            data={
                "uid": uid,
                "date": date,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Fan overview data retrieved successfully"
        )

    except ValueError as ve:
        logger.warning(f"Invalid parameters in fan overview request: {ve}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(ve)
        )
    except Exception as e:
        logger.error(f"Error fetching creator fan overview by date: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fetch fan overview data by date: {str(e)}"
        )


@app.get("/creator/video/compare/by-date-range")
async def read_creator_video_compare_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Get video comparison data within a date range.

    Args:
        uid: User ID
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        limit: Maximum number of records to return (default: 1000)
        offset: Pagination offset (default: 0)

    Returns:
        Standard API response with video comparison data

    Example:
        GET /creator/video/compare/by-date-range?uid=401315430&start_date=2025-01-01&end_date=2025-01-31

        Response:
        {
            "code": 200,
            "message": "Video comparison data retrieved successfully",
            "data": {
                "uid": "401315430",
                "date_range": {
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-31"
                },
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 31
                },
                "list": [...],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not uid or not uid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("User ID is required", 400)
            )

        if not start_date or not start_date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Start date is required", 400)
            )

        if not end_date or not end_date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("End date is required", 400)
            )

        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Limit must be greater than 0", 400)
            )

        if offset < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Offset must be non-negative", 400)
            )

        # Query video comparison data
        results, total_count = await query_video_compare_by_date_range(
            uid, start_date, end_date, limit, offset
        )

        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response("No video comparison data found", 404)
            )

        return create_success_response(
            data={
                "uid": uid,
                "date_range": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Video comparison data retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get video comparison data by date range for uid {uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video comparison data", 500)
        )


@app.get("/creator/video/compare/by-date")
async def read_creator_video_compare_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Get video comparison data for a specific date.

    Args:
        uid: User ID
        date: Query date in YYYY-MM-DD format
        limit: Maximum number of records to return (default: 1000)
        offset: Pagination offset (default: 0)

    Returns:
        Standard API response with video comparison data

    Example:
        GET /creator/video/compare/by-date?uid=401315430&date=2025-01-15

        Response:
        {
            "code": 200,
            "message": "Video comparison data retrieved successfully",
            "data": {
                "uid": "401315430",
                "date": "2025-01-15",
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 1
                },
                "list": [...],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not uid or not uid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("User ID is required", 400)
            )

        if not date or not date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Date is required", 400)
            )

        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Limit must be greater than 0", 400)
            )

        if offset < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Offset must be non-negative", 400)
            )

        # Query video comparison data
        results, total_count = await query_video_compare_by_date(
            uid, date, limit, offset
        )

        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response("No video comparison data found", 404)
            )

        return create_success_response(
            data={
                "uid": uid,
                "date": date,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Video comparison data retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get video comparison data by date for uid {uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video comparison data", 500)
        )


@app.get("/creator/video/pandect/by-date-range")
async def read_creator_video_pandect_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    data_type_column: Optional[str] = None,
    limit: int = 1000,
    offset: int = 0
):
    """
    Get video trend data within a date range.

    Args:
        uid: User ID
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        data_type_column: Optional data type column filter
        limit: Maximum number of records to return (default: 1000)
        offset: Pagination offset (default: 0)

    Returns:
        Standard API response with video trend data

    Example:
        GET /creator/video/pandect/by-date-range?uid=401315430&start_date=2025-01-01&end_date=2025-01-31&data_type_column=views

        Response:
        {
            "code": 200,
            "message": "Video trend data retrieved successfully",
            "data": {
                "uid": "401315430",
                "date_range": {
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-31"
                },
                "data_type_column": "views",
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 31
                },
                "list": [...],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not uid or not uid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("User ID is required", 400)
            )

        if not start_date or not start_date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Start date is required", 400)
            )

        if not end_date or not end_date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("End date is required", 400)
            )

        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Limit must be greater than 0", 400)
            )

        if offset < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Offset must be non-negative", 400)
            )

        # Query video trend data
        results, total_count = await query_video_pandect_by_date_range(
            uid, start_date, end_date, data_type_column, limit, offset
        )

        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response("No video trend data found", 404)
            )

        return create_success_response(
            data={
                "uid": uid,
                "date_range": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "data_type_column": data_type_column,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Video trend data retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get video trend data by date range for uid {uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video trend data", 500)
        )


@app.get("/creator/video/pandect/by-date")
async def read_creator_video_pandect_by_date(
    uid: str,
    date: str,
    data_type_column: Optional[str] = None,
    limit: int = 1000,
    offset: int = 0
):
    """
    Get video trend data for a specific date.

    Args:
        uid: User ID
        date: Query date in YYYY-MM-DD format
        data_type_column: Optional data type column filter
        limit: Maximum number of records to return (default: 1000)
        offset: Pagination offset (default: 0)

    Returns:
        Standard API response with video trend data

    Example:
        GET /creator/video/pandect/by-date?uid=401315430&date=2025-01-15&data_type_column=views

        Response:
        {
            "code": 200,
            "message": "Video trend data retrieved successfully",
            "data": {
                "uid": "401315430",
                "date": "2025-01-15",
                "data_type_column": "views",
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 1
                },
                "list": [...],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not uid or not uid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("User ID is required", 400)
            )

        if not date or not date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Date is required", 400)
            )

        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Limit must be greater than 0", 400)
            )

        if offset < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Offset must be non-negative", 400)
            )

        # Query video trend data
        results, total_count = await query_video_pandect_by_date(
            uid, date, data_type_column, limit, offset
        )

        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response("No video trend data found", 404)
            )

        return create_success_response(
            data={
                "uid": uid,
                "date": date,
                "data_type_column": data_type_column,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Video trend data retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get video trend data by date for uid {uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video trend data", 500)
        )


@app.get("/creator/video/survey/by-date-range")
async def read_creator_video_survey_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    data_type: Optional[int] = None,
    limit: int = 1000,
    offset: int = 0
):
    """
    Get video survey data within a date range.

    Args:
        uid: User ID
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        data_type: Optional data type filter
        limit: Maximum number of records to return (default: 1000)
        offset: Pagination offset (default: 0)

    Returns:
        Standard API response with video survey data

    Example:
        GET /creator/video/survey/by-date-range?uid=401315430&start_date=2025-01-01&end_date=2025-01-31&data_type=1

        Response:
        {
            "code": 200,
            "message": "Video survey data retrieved successfully",
            "data": {
                "uid": "401315430",
                "date_range": {
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-31"
                },
                "data_type": 1,
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 31
                },
                "list": [...],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not uid or not uid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("User ID is required", 400)
            )

        if not start_date or not start_date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Start date is required", 400)
            )

        if not end_date or not end_date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("End date is required", 400)
            )

        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Limit must be greater than 0", 400)
            )

        if offset < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Offset must be non-negative", 400)
            )

        # Query video survey data
        results, total_count = await query_video_survey_by_date_range(
            uid, start_date, end_date, data_type, limit, offset
        )

        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response("No video survey data found", 404)
            )

        return create_success_response(
            data={
                "uid": uid,
                "date_range": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "data_type": data_type,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Video survey data retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get video survey data by date range for uid {uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video survey data", 500)
        )


@app.get("/creator/video/survey/by-date")
async def read_creator_video_survey_by_date(
    uid: str,
    date: str,
    data_type: Optional[int] = None,
    limit: int = 1000,
    offset: int = 0
):
    """
    Get video survey data for a specific date.

    Args:
        uid: User ID
        date: Query date in YYYY-MM-DD format
        data_type: Optional data type filter
        limit: Maximum number of records to return (default: 1000)
        offset: Pagination offset (default: 0)

    Returns:
        Standard API response with video survey data

    Example:
        GET /creator/video/survey/by-date?uid=401315430&date=2025-01-15&data_type=1

        Response:
        {
            "code": 200,
            "message": "Video survey data retrieved successfully",
            "data": {
                "uid": "401315430",
                "date": "2025-01-15",
                "data_type": 1,
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 1
                },
                "list": [...],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not uid or not uid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("User ID is required", 400)
            )

        if not date or not date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Date is required", 400)
            )

        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Limit must be greater than 0", 400)
            )

        if offset < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Offset must be non-negative", 400)
            )

        # Query video survey data
        results, total_count = await query_video_survey_by_date(
            uid, date, data_type, limit, offset
        )

        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response("No video survey data found", 404)
            )

        return create_success_response(
            data={
                "uid": uid,
                "date": date,
                "data_type": data_type,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Video survey data retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get video survey data by date for uid {uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video survey data", 500)
        )


@app.get("/creator/video/source/by-date-range")
async def read_creator_video_source_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Get video source data within a date range.

    Args:
        uid: User ID
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        limit: Maximum number of records to return (default: 1000)
        offset: Pagination offset (default: 0)

    Returns:
        Standard API response with video source data

    Example:
        GET /creator/video/source/by-date-range?uid=401315430&start_date=2025-01-01&end_date=2025-01-31

        Response:
        {
            "code": 200,
            "message": "Video source data retrieved successfully",
            "data": {
                "uid": "401315430",
                "date_range": {
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-31"
                },
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 31
                },
                "list": [...],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not uid or not uid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("User ID is required", 400)
            )

        if not start_date or not start_date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Start date is required", 400)
            )

        if not end_date or not end_date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("End date is required", 400)
            )

        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Limit must be greater than 0", 400)
            )

        if offset < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Offset must be non-negative", 400)
            )

        # Query video source data
        results, total_count = await query_video_source_by_date_range(
            uid, start_date, end_date, limit, offset
        )

        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response("No video source data found", 404)
            )

        return create_success_response(
            data={
                "uid": uid,
                "date_range": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Video source data retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get video source data by date range for uid {uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video source data", 500)
        )


@app.get("/creator/video/source/by-date")
async def read_creator_video_source_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Get video source data for a specific date.

    Args:
        uid: User ID
        date: Query date in YYYY-MM-DD format
        limit: Maximum number of records to return (default: 1000)
        offset: Pagination offset (default: 0)

    Returns:
        Standard API response with video source data

    Example:
        GET /creator/video/source/by-date?uid=401315430&date=2025-01-15

        Response:
        {
            "code": 200,
            "message": "Video source data retrieved successfully",
            "data": {
                "uid": "401315430",
                "date": "2025-01-15",
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 1
                },
                "list": [...],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not uid or not uid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("User ID is required", 400)
            )

        if not date or not date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Date is required", 400)
            )

        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Limit must be greater than 0", 400)
            )

        if offset < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Offset must be non-negative", 400)
            )

        # Query video source data
        results, total_count = await query_video_source_by_date(
            uid, date, limit, offset
        )

        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response("No video source data found", 404)
            )

        return create_success_response(
            data={
                "uid": uid,
                "date": date,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Video source data retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get video source data by date for uid {uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video source data", 500)
        )


@app.get("/creator/video/view_data/by-date-range")
async def read_creator_video_view_data_by_date_range(
    uid: str,
    start_date: str,
    end_date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Get creator's video viewing data within a date range.

    Args:
        uid: User ID
        start_date: Start date in YYYY-MM-DD format
        end_date: End date in YYYY-MM-DD format
        limit: Maximum number of records to return (default: 1000)
        offset: Pagination offset (default: 0)

    Returns:
        Standard API response with video viewing data

    Example:
        GET /creator/video/view_data/by-date-range?uid=401315430&start_date=2025-01-01&end_date=2025-01-31

        Response:
        {
            "code": 200,
            "message": "Video viewing data retrieved successfully",
            "data": {
                "uid": "401315430",
                "date_range": {
                    "start_date": "2025-01-01",
                    "end_date": "2025-01-31"
                },
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 31
                },
                "list": [...],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not uid or not uid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("User ID is required", 400)
            )

        if not start_date or not start_date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Start date is required", 400)
            )

        if not end_date or not end_date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("End date is required", 400)
            )

        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Limit must be greater than 0", 400)
            )

        if offset < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Offset must be non-negative", 400)
            )

        # Query video view data
        results, total_count = await query_video_view_data_by_date_range(
            uid, start_date, end_date, limit, offset
        )

        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response("No video viewing data found", 404)
            )

        return create_success_response(
            data={
                "uid": uid,
                "date_range": {
                    "start_date": start_date,
                    "end_date": end_date
                },
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Video viewing data retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get video viewing data by date range for uid {uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video viewing data", 500)
        )


@app.get("/creator/video/view_data/by-date")
async def read_creator_video_view_data_by_date(
    uid: str,
    date: str,
    limit: int = 1000,
    offset: int = 0
):
    """
    Get creator's video viewing data for a specific date.

    Args:
        uid: User ID
        date: Query date in YYYY-MM-DD format
        limit: Maximum number of records to return (default: 1000)
        offset: Pagination offset (default: 0)

    Returns:
        Standard API response with video viewing data

    Example:
        GET /creator/video/view_data/by-date?uid=401315430&date=2025-01-15

        Response:
        {
            "code": 200,
            "message": "Video viewing data retrieved successfully",
            "data": {
                "uid": "401315430",
                "date": "2025-01-15",
                "pagination": {
                    "limit": 1000,
                    "offset": 0,
                    "total": 1
                },
                "list": [...],
                "retrieved_at": "2025-08-04T12:00:00"
            }
        }
    """
    try:
        # Parameter validation
        if not uid or not uid.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("User ID is required", 400)
            )

        if not date or not date.strip():
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Date is required", 400)
            )

        if limit <= 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Limit must be greater than 0", 400)
            )

        if offset < 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=create_error_response("Offset must be non-negative", 400)
            )

        # Query video view data
        results, total_count = await query_video_view_data_by_date(
            uid, date, limit, offset
        )

        if not results:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=create_error_response("No video viewing data found", 404)
            )

        return create_success_response(
            data={
                "uid": uid,
                "date": date,
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "total": total_count
                },
                "list": results,
                "retrieved_at": datetime.now().isoformat()
            },
            message="Video viewing data retrieved successfully"
        )

    except HTTPException:
        # Re-raise HTTP exceptions as they are already properly formatted
        raise
    except Exception as e:
        logger.error(f"Failed to get video viewing data by date for uid {uid}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=create_error_response("Failed to retrieve video viewing data", 500)
        )


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app="app:app", host="0.0.0.0", port=9022, reload=False, workers=1)