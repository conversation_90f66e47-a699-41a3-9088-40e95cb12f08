import jieba
from wordcloud import WordCloud
import numpy as np
from PIL import Image
import os
import platform
from const import PROJECT_ROOT
from utils import get_zh_role_name
from logger import logger

# Load stopwords with error handling
try:
    STOPWORDS_ZH = set(
        map(
            str.strip,
            open(
                f"{PROJECT_ROOT}/utils/stop_words_zh.txt", encoding="utf-8"
            ).readlines(),
        )
    )
except FileNotFoundError:
    logger.warning("Chinese stopwords file not found, using default stopwords")
    STOPWORDS_ZH = set(['的', '了', '在', '是', '我', '有', '和', '就', '不', '人', '都', '一', '一个', '上', '也', '很', '到', '说', '要', '去', '你', '会', '着', '没有', '看', '好', '自己', '这'])


def get_system_font_path():
    """Get a suitable Chinese font path based on the operating system"""
    system = platform.system()

    # Try custom font first
    custom_font = f"{PROJECT_ROOT}/backend/utils/fonts/msyh.ttf"
    if os.path.exists(custom_font):
        return custom_font

    # System-specific font paths
    if system == "Windows":
        fonts = [
            "C:/Windows/Fonts/msyh.ttc",  # Microsoft YaHei
            "C:/Windows/Fonts/simhei.ttf",  # SimHei
            "C:/Windows/Fonts/simsun.ttc",  # SimSun
            "C:/Windows/Fonts/arial.ttf",  # Fallback
        ]
    elif system == "Darwin":  # macOS
        fonts = [
            "/System/Library/Fonts/PingFang.ttc",
            "/System/Library/Fonts/STHeiti Light.ttc",
            "/System/Library/Fonts/Arial.ttf",
        ]
    else:  # Linux
        fonts = [
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/TTF/arial.ttf",
        ]

    for font_path in fonts:
        if os.path.exists(font_path):
            return font_path

    logger.warning("No suitable font found, using default font")
    return None


def word_cloud_gen(context_list: list, target_path: str, vtuber_name="xingtong"):
    """
    Generate word cloud with robust error handling for missing resources.

    Args:
        context_list: List of text strings to generate word cloud from
        target_path: Output file path for the word cloud image
        vtuber_name: VTuber name for mask image selection
    """
    try:
        char_zh = get_zh_role_name(vtuber_name)

        # Try to load mask image, fallback to None if not found
        mask_image = None
        mask_path = f"{PROJECT_ROOT}/data/images/vtubers/{char_zh}.jpg"

        if os.path.exists(mask_path):
            try:
                mask_image = np.array(Image.open(mask_path))
                logger.info(f"Using mask image: {mask_path}")
            except Exception as e:
                logger.warning(f"Failed to load mask image {mask_path}: {e}")
                mask_image = None
        else:
            logger.warning(f"Mask image not found: {mask_path}, using default shape")

        # Process text
        if not context_list:
            raise ValueError("No text content provided for word cloud generation")

        context = " ".join(context_list)
        words = jieba.lcut(context)
        newtxt = " ".join(words)

        if not newtxt.strip():
            raise ValueError("No valid words found after text processing")

        # Get font path
        font_path = get_system_font_path()

        # Create output directory if it doesn't exist
        os.makedirs(os.path.dirname(target_path), exist_ok=True)

        # Generate word cloud with robust parameters
        wordcloud_params = {
            "width": 800,
            "height": 800,
            "background_color": "white",
            "stopwords": STOPWORDS_ZH,
            "max_words": 500,
            "min_font_size": 2,
            "contour_width": 0.5,
            "contour_color": "yellow",
            "relative_scaling": 0.5,
            "random_state": 42,  # For reproducible results
        }

        if mask_image is not None:
            wordcloud_params["mask"] = mask_image

        if font_path:
            wordcloud_params["font_path"] = font_path

        wordcloud = WordCloud(**wordcloud_params).generate(newtxt)
        wordcloud.to_file(target_path)

        logger.info(f"Word cloud generated successfully: {target_path}")

    except Exception as e:
        logger.error(f"Error generating word cloud: {e}")
        raise
