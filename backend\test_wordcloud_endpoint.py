#!/usr/bin/env python3
"""
Test script for the /comment/wordcloud API endpoint.

This script tests the word cloud generation functionality with proper error handling
and caching mechanisms.
"""

import asyncio
import time
import os
import sys
from datetime import datetime

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.query.query_user_data import query_comment_wordcloud
from utils.word_cloud_gen import word_cloud_gen, get_system_font_path
from logger import logger


async def test_word_cloud_generation():
    """Test word cloud generation with sample data"""
    
    print("🎨 WORD CLOUD ENDPOINT TEST")
    print("=" * 50)
    
    # Test parameters
    test_mid = "401315430"  # 星瞳's MID
    test_char_zh = "星瞳"
    test_start_date = "2024-01-01"
    test_end_date = "2024-12-31"
    
    print(f"\n1. Testing word cloud generation...")
    print(f"   MID: {test_mid}")
    print(f"   Character: {test_char_zh}")
    print(f"   Date range: {test_start_date} to {test_end_date}")
    
    try:
        start_time = time.perf_counter()
        
        # Test the word cloud generation function
        result_path = await query_comment_wordcloud(test_mid, test_char_zh, test_start_date, test_end_date)
        
        end_time = time.perf_counter()
        execution_time = end_time - start_time
        
        if result_path:
            print(f"   ✅ Word cloud generated successfully!")
            print(f"   📁 Path: {result_path}")
            print(f"   ⏱️  Generation time: {execution_time:.3f}s")
            
            # Check if file actually exists
            full_path = f"frontend/public{result_path}"
            if os.path.exists(full_path):
                file_size = os.path.getsize(full_path)
                print(f"   📊 File size: {file_size:,} bytes")
            else:
                print(f"   ❌ Warning: Generated file not found at {full_path}")
                
        else:
            print(f"   ❌ Word cloud generation failed")
            
    except Exception as e:
        print(f"   ❌ Error during word cloud generation: {e}")
        logger.error(f"Word cloud test failed: {e}")


async def test_caching_mechanism():
    """Test the caching mechanism"""
    
    print(f"\n2. Testing caching mechanism...")
    
    test_mid = "401315430"
    test_char_zh = "星瞳"
    test_start_date = "2024-06-01"
    test_end_date = "2024-12-31"
    
    try:
        # First request (should generate new word cloud)
        print(f"   First request (cache miss)...")
        start_time = time.perf_counter()
        result1 = await query_comment_wordcloud(test_mid, test_char_zh, test_start_date, test_end_date)
        first_time = time.perf_counter() - start_time
        
        # Second request (should use cache)
        print(f"   Second request (cache hit)...")
        start_time = time.perf_counter()
        result2 = await query_comment_wordcloud(test_mid, test_char_zh, test_start_date, test_end_date)
        second_time = time.perf_counter() - start_time
        
        if result1 and result2:
            print(f"   ✅ Caching test successful!")
            print(f"   📊 First request: {first_time:.3f}s")
            print(f"   📊 Second request: {second_time:.3f}s")
            
            if second_time < first_time:
                speedup = first_time / second_time
                print(f"   🚀 Cache speedup: {speedup:.1f}x faster")
            else:
                print(f"   ⚠️  Cache may not be working as expected")
                
        else:
            print(f"   ❌ Caching test failed - one or both requests failed")
            
    except Exception as e:
        print(f"   ❌ Error during caching test: {e}")


def test_font_detection():
    """Test font detection and fallback mechanism"""
    
    print(f"\n3. Testing font detection...")
    
    try:
        font_path = get_system_font_path()
        
        if font_path:
            print(f"   ✅ Font detected: {font_path}")
            if os.path.exists(font_path):
                print(f"   ✅ Font file exists and is accessible")
            else:
                print(f"   ❌ Font file path returned but file doesn't exist")
        else:
            print(f"   ⚠️  No font detected, will use default font")
            
    except Exception as e:
        print(f"   ❌ Error during font detection: {e}")


def test_sample_word_cloud():
    """Test word cloud generation with sample text"""
    
    print(f"\n4. Testing sample word cloud generation...")
    
    try:
        # Sample Chinese text
        sample_texts = [
            "星瞳真的太可爱了",
            "今天的直播很有趣",
            "感谢星瞳的精彩表演",
            "期待下次直播",
            "星瞳加油",
            "很喜欢这个游戏",
            "笑死我了哈哈哈",
            "星瞳的声音很好听",
            "这个梗太好笑了",
            "支持星瞳"
        ]
        
        # Create test output directory
        test_output_dir = "frontend/public/wordcloud/test"
        os.makedirs(test_output_dir, exist_ok=True)
        
        test_output_path = f"{test_output_dir}/sample_wordcloud.png"
        
        # Generate word cloud
        word_cloud_gen(sample_texts, test_output_path, "星瞳")
        
        if os.path.exists(test_output_path):
            file_size = os.path.getsize(test_output_path)
            print(f"   ✅ Sample word cloud generated successfully!")
            print(f"   📁 Path: {test_output_path}")
            print(f"   📊 File size: {file_size:,} bytes")
        else:
            print(f"   ❌ Sample word cloud generation failed")
            
    except Exception as e:
        print(f"   ❌ Error during sample word cloud generation: {e}")


async def run_comprehensive_test():
    """Run comprehensive tests for word cloud functionality"""
    
    print("🧪 COMPREHENSIVE WORD CLOUD TESTS")
    print("=" * 80)
    
    # Test font detection first
    test_font_detection()
    
    # Test sample word cloud generation
    test_sample_word_cloud()
    
    # Test actual word cloud generation
    await test_word_cloud_generation()
    
    # Test caching mechanism
    await test_caching_mechanism()
    
    print(f"\n" + "=" * 80)
    print("✅ WORD CLOUD TESTS COMPLETED")
    print("=" * 80)
    
    print(f"\nTest Summary:")
    print(f"• Font detection and fallback mechanism tested")
    print(f"• Sample word cloud generation tested")
    print(f"• Database-driven word cloud generation tested")
    print(f"• Caching mechanism tested")
    print(f"\nThe /comment/wordcloud endpoint should now work correctly!")


if __name__ == "__main__":
    async def main():
        try:
            await run_comprehensive_test()
            
        except Exception as e:
            logger.error(f"Word cloud test failed: {e}")
            print(f"❌ Word cloud test failed: {e}")
    
    asyncio.run(main())
