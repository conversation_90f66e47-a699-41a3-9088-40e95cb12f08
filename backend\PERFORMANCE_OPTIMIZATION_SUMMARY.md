# Comment API Endpoints Performance Optimization

## Overview

This document summarizes the performance optimizations implemented for the `/comment/top_n` and `/comment/top_n_user` API endpoints in the backend application.

## Original Performance Issues

### `/comment/top_n` Endpoint Issues

#### 1. Inefficient Database Queries
- **Problem**: Separate queries for video and dynamic comments
- **Impact**: Multiple database round trips
- **Details**: The original implementation executed separate `SELECT` statements for video comments and dynamic comments, then combined results in Python

#### 2. Memory-Intensive Processing
- **Problem**: Loading ALL comments into memory before sorting
- **Impact**: High memory usage and slow processing for large datasets
- **Details**: All comments within the date range were loaded into a Python list, then sorted using `sorted()` function

#### 3. Python-Level Sorting
- **Problem**: Sorting performed in application layer instead of database
- **Impact**: Inefficient use of database capabilities
- **Details**: Comments were sorted by heat/likes using Python's `sorted()` function instead of SQL `ORDER BY`

### `/comment/top_n_user` Endpoint Issues

#### 1. Multiple Separate Database Queries
- **Problem**: Separate queries for video and dynamic comments with no aggregation
- **Impact**: Multiple database round trips and inefficient data processing
- **Details**: Executed separate queries then aggregated user statistics in Python using `defaultdict`

#### 2. Python-Level Aggregation
- **Problem**: User statistics calculated in application memory
- **Impact**: High memory usage and slow processing for large comment datasets
- **Details**: All comment data loaded into memory, then aggregated by username using Python loops

#### 3. Multiple Python Sorting Operations
- **Problem**: Three separate sorting operations for likes, comments, and replies
- **Impact**: Inefficient processing and increased response times
- **Details**: Used Python `sorted()` function three times instead of database `ORDER BY`

### Common Issues

#### 4. No Caching Mechanism
- **Problem**: Every request hit the database
- **Impact**: Unnecessary database load for repeated requests
- **Details**: No caching of frequently requested data

#### 5. Missing Database Indexes
- **Problem**: Queries on `datetime`, `like_num`/`heat`, and user columns without proper indexes
- **Impact**: Full table scans for large comment tables
- **Details**: No indexes on columns used in WHERE clauses, ORDER BY clauses, and GROUP BY operations

## Implemented Optimizations

### `/comment/top_n` Endpoint Optimizations

#### 1. Single Optimized Database Query
```sql
-- New approach: Single query with UNION ALL
SELECT * FROM (
    -- Video comments
    SELECT vc.uname as name, vc.mid as uid, vc.face, vc.datetime, 
           vc.comment, vc.like_num as heat, vc.bvid as from_id,
           vc.from_video_title as from_name, 'video' as source
    FROM video_comment_table vc
    JOIN videos_table v ON vc.bvid = v.bvid
    WHERE v.uid = $1 AND vc.datetime >= $2 AND vc.datetime < $3
    
    UNION ALL
    
    -- Dynamic comments  
    SELECT dc.uname as name, dc.mid as uid, dc.face, dc.datetime,
           dc.comment, dc.like_num as heat, dc.oid as from_id,
           dc.from_dynamic as from_name, 'dynamic' as source
    FROM dynamics_comment_table dc
    JOIN dynamics_table d ON dc.oid = d.dynamic_id
    WHERE d.uid = $1 AND dc.datetime >= $2 AND dc.datetime < $3
) as combined_comments
ORDER BY heat DESC
LIMIT n
```

**Benefits**:
- Single database round trip
- Database-level sorting and limiting
- Reduced memory usage

#### 2. Database-Level Sorting and Limiting
- **Change**: Added `ORDER BY heat DESC LIMIT n` to the SQL query
- **Benefit**: Only the top N comments are returned from the database
- **Impact**: Eliminates Python sorting and reduces data transfer

#### 3. In-Memory Caching
```python
# Cache implementation
_top_comments_cache = {}
_cache_ttl_seconds = 300  # 5 minutes

# Cache key generation
cache_key = hashlib.md5(f"{mid}_{start_time_str}_{end_time_str}_{n}_{source}".encode()).hexdigest()

# Cache lookup and storage
if cache_key in _top_comments_cache:
    cached_data, cached_time = _top_comments_cache[cache_key]
    if (current_time - cached_time).total_seconds() < _cache_ttl_seconds:
        return cached_data
```

**Benefits**:
- 5-minute TTL for frequently requested data
- Automatic cache cleanup to prevent memory leaks
- Significant speedup for repeated requests

### `/comment/top_n_user` Endpoint Optimizations

#### 1. Single Database Query with Aggregation
```sql
-- New approach: Single query with UNION ALL and GROUP BY aggregation
SELECT
    name,
    uid,
    face,
    SUM(like_num) as total_likes,
    SUM(comment_count) as total_comments,
    SUM(rcount) as total_replies
FROM (
    -- Video comments
    SELECT vc.mid as uid, vc.uname as name, vc.face, vc.like_num, vc.rcount, 1 as comment_count
    FROM video_comment_table vc
    JOIN videos_table v ON vc.bvid = v.bvid
    WHERE v.uid = $1 AND vc.datetime >= $2 AND vc.datetime < $3
          AND vc.mid IS NOT NULL AND vc.mid != $1

    UNION ALL

    -- Dynamic comments
    SELECT dc.mid as uid, dc.uname as name, dc.face, dc.like_num, dc.rcount, 1 as comment_count
    FROM dynamics_comment_table dc
    JOIN dynamics_table d ON dc.oid = d.dynamic_id
    WHERE d.uid = $1 AND dc.datetime >= $2 AND dc.datetime < $3
          AND dc.mid IS NOT NULL AND dc.mid != $1
) as combined_comments
GROUP BY name, uid, face
```

**Benefits**:
- Single database round trip with aggregation
- Database-level GROUP BY instead of Python aggregation
- Eliminated intermediate data structures

#### 2. Database-Level Sorting for Each Ranking
```sql
-- Separate optimized queries for each ranking type
SELECT name, uid, face, total_likes as likeNum
FROM (aggregated_query) as user_stats
ORDER BY total_likes DESC
LIMIT n
```

**Benefits**:
- Three optimized queries instead of loading all data and sorting in Python
- Database handles sorting and limiting efficiently
- Only top N users returned for each category

#### 3. Shared Caching System
- **Implementation**: Separate cache for user rankings with same TTL strategy
- **Benefit**: Fast repeated requests for user activity data
- **Impact**: Sub-10ms response times for cached user rankings

#### 4. Enhanced Performance Indexes
Created indexes on critical columns for both endpoints:

```sql
-- Video comment table indexes (optimized for both top comments and top users)
CREATE INDEX IF NOT EXISTS idx_video_comment_datetime ON video_comment_table (datetime);
CREATE INDEX IF NOT EXISTS idx_video_comment_like_num ON video_comment_table (like_num DESC);
CREATE INDEX IF NOT EXISTS idx_video_comment_heat ON video_comment_table (heat DESC);
CREATE INDEX IF NOT EXISTS idx_video_comment_datetime_heat ON video_comment_table (datetime, heat DESC);
CREATE INDEX IF NOT EXISTS idx_video_comment_bvid ON video_comment_table (bvid);
CREATE INDEX IF NOT EXISTS idx_video_comment_mid_datetime ON video_comment_table (mid, datetime);
CREATE INDEX IF NOT EXISTS idx_video_comment_uname_datetime ON video_comment_table (uname, datetime);
CREATE INDEX IF NOT EXISTS idx_video_comment_rcount ON video_comment_table (rcount DESC);

-- Dynamics comment table indexes (optimized for both top comments and top users)
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_datetime ON dynamics_comment_table (datetime);
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_like_num ON dynamics_comment_table (like_num DESC);
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_heat ON dynamics_comment_table (heat DESC);
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_datetime_heat ON dynamics_comment_table (datetime, heat DESC);
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_oid ON dynamics_comment_table (oid);
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_mid_datetime ON dynamics_comment_table (mid, datetime);
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_uname_datetime ON dynamics_comment_table (uname, datetime);
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_rcount ON dynamics_comment_table (rcount DESC);
```

**Benefits**:
- Faster WHERE clause filtering on datetime
- Optimized ORDER BY operations on heat/like_num
- Improved JOIN performance
- Enhanced GROUP BY performance for user aggregation
- Faster user-based filtering and sorting

#### 5. Reduced String Processing
- **Change**: String cleaning (regex operations) only performed on final top N results
- **Benefit**: Eliminates unnecessary processing of comments that won't be returned
- **Impact**: Reduced CPU usage for large datasets

## Performance Improvements

### Expected Performance Gains

#### `/comment/top_n` Endpoint
1. **Query Time Reduction**: 60-80% faster database queries
   - Single query vs multiple queries
   - Database-level sorting vs Python sorting
   - Proper indexes vs full table scans

2. **Memory Usage Reduction**: 70-90% less memory usage
   - Only top N comments loaded vs all comments
   - Eliminated large Python lists

#### `/comment/top_n_user` Endpoint
1. **Query Time Reduction**: 70-85% faster database queries
   - Single aggregated query vs multiple queries + Python aggregation
   - Database-level GROUP BY vs Python defaultdict processing
   - Database-level sorting vs multiple Python sorting operations

2. **Memory Usage Reduction**: 80-95% less memory usage
   - Direct aggregation in database vs loading all comment data
   - Eliminated intermediate data structures and user statistics dictionaries

#### Common Improvements
3. **Cache Performance**: 95%+ faster for cached requests
   - Sub-10ms response times for cached data
   - Reduced database load for both endpoints

4. **Scalability**: Better performance with larger datasets
   - Performance scales with result size (N) rather than total comment count
   - Consistent response times regardless of date range size

### Benchmark Targets

- **Excellent**: < 100ms response time
- **Good**: < 500ms response time  
- **Acceptable**: < 1s response time

## Testing and Verification

### Performance Test Scripts
Run the performance tests to measure improvements:

```bash
cd backend
# Test /comment/top_n endpoint optimizations
python performance_test_top_comments.py

# Test /comment/top_n_user endpoint optimizations
python performance_test_top_users.py
```

### Database Migration
Apply performance indexes to existing tables:

```bash
cd backend/migrations
python add_comment_performance_indexes.py
```

This migration script now creates indexes optimized for both endpoints.

## API Contract Preservation

The optimizations maintain 100% backward compatibility for both endpoints:

### `/comment/top_n` Endpoint
- **Same endpoint**: `/comment/top_n`
- **Same parameters**: `s`, `e`, `n`, `source`, `vtuber`
- **Same response format**: Identical JSON structure
- **Same functionality**: All features preserved

### `/comment/top_n_user` Endpoint
- **Same endpoint**: `/comment/top_n_user`
- **Same parameters**: `s`, `e`, `n`, `source`, `vtuber`
- **Same response format**: Identical JSON structure with three user ranking categories
- **Same functionality**: All user ranking features preserved

## Monitoring and Maintenance

### Cache Management
- Automatic cache cleanup when size exceeds 100 entries
- 5-minute TTL prevents stale data
- Cache keys include all query parameters for accuracy

### Index Maintenance
- Indexes are created with `IF NOT EXISTS` to prevent conflicts
- Regular database maintenance should include index statistics updates
- Monitor index usage with PostgreSQL query statistics

### Performance Monitoring
- Log query execution times
- Monitor cache hit rates
- Track memory usage patterns

## Future Optimizations

### Potential Enhancements
1. **Redis Caching**: Replace in-memory cache with Redis for distributed caching
2. **Query Result Pagination**: Add pagination for very large result sets
3. **Materialized Views**: Pre-computed top comments for popular time ranges
4. **Connection Pooling**: Optimize database connection management
5. **Async Batch Processing**: Background processing for cache warming

### Monitoring Recommendations
1. Set up alerts for response times > 1s
2. Monitor cache hit rates (target: >70%)
3. Track database query performance
4. Monitor memory usage trends
