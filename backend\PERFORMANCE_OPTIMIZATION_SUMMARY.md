# /comment/top_n API Endpoint Performance Optimization

## Overview

This document summarizes the performance optimizations implemented for the `/comment/top_n` API endpoint in the backend application.

## Original Performance Issues

### 1. Inefficient Database Queries
- **Problem**: Separate queries for video and dynamic comments
- **Impact**: Multiple database round trips
- **Details**: The original implementation executed separate `SELECT` statements for video comments and dynamic comments, then combined results in Python

### 2. Memory-Intensive Processing
- **Problem**: Loading ALL comments into memory before sorting
- **Impact**: High memory usage and slow processing for large datasets
- **Details**: All comments within the date range were loaded into a Python list, then sorted using `sorted()` function

### 3. Python-Level Sorting
- **Problem**: Sorting performed in application layer instead of database
- **Impact**: Inefficient use of database capabilities
- **Details**: Comments were sorted by heat/likes using Python's `sorted()` function instead of SQL `ORDER BY`

### 4. No Caching Mechanism
- **Problem**: Every request hit the database
- **Impact**: Unnecessary database load for repeated requests
- **Details**: No caching of frequently requested data

### 5. Missing Database Indexes
- **Problem**: Queries on `datetime` and `like_num`/`heat` columns without proper indexes
- **Impact**: Full table scans for large comment tables
- **Details**: No indexes on columns used in WHERE clauses and ORDER BY clauses

## Implemented Optimizations

### 1. Single Optimized Database Query
```sql
-- New approach: Single query with UNION ALL
SELECT * FROM (
    -- Video comments
    SELECT vc.uname as name, vc.mid as uid, vc.face, vc.datetime, 
           vc.comment, vc.like_num as heat, vc.bvid as from_id,
           vc.from_video_title as from_name, 'video' as source
    FROM video_comment_table vc
    JOIN videos_table v ON vc.bvid = v.bvid
    WHERE v.uid = $1 AND vc.datetime >= $2 AND vc.datetime < $3
    
    UNION ALL
    
    -- Dynamic comments  
    SELECT dc.uname as name, dc.mid as uid, dc.face, dc.datetime,
           dc.comment, dc.like_num as heat, dc.oid as from_id,
           dc.from_dynamic as from_name, 'dynamic' as source
    FROM dynamics_comment_table dc
    JOIN dynamics_table d ON dc.oid = d.dynamic_id
    WHERE d.uid = $1 AND dc.datetime >= $2 AND dc.datetime < $3
) as combined_comments
ORDER BY heat DESC
LIMIT n
```

**Benefits**:
- Single database round trip
- Database-level sorting and limiting
- Reduced memory usage

### 2. Database-Level Sorting and Limiting
- **Change**: Added `ORDER BY heat DESC LIMIT n` to the SQL query
- **Benefit**: Only the top N comments are returned from the database
- **Impact**: Eliminates Python sorting and reduces data transfer

### 3. In-Memory Caching
```python
# Cache implementation
_top_comments_cache = {}
_cache_ttl_seconds = 300  # 5 minutes

# Cache key generation
cache_key = hashlib.md5(f"{mid}_{start_time_str}_{end_time_str}_{n}_{source}".encode()).hexdigest()

# Cache lookup and storage
if cache_key in _top_comments_cache:
    cached_data, cached_time = _top_comments_cache[cache_key]
    if (current_time - cached_time).total_seconds() < _cache_ttl_seconds:
        return cached_data
```

**Benefits**:
- 5-minute TTL for frequently requested data
- Automatic cache cleanup to prevent memory leaks
- Significant speedup for repeated requests

### 4. Performance Indexes
Created indexes on critical columns:

```sql
-- Video comment table indexes
CREATE INDEX IF NOT EXISTS idx_video_comment_datetime ON video_comment_table (datetime);
CREATE INDEX IF NOT EXISTS idx_video_comment_like_num ON video_comment_table (like_num DESC);
CREATE INDEX IF NOT EXISTS idx_video_comment_heat ON video_comment_table (heat DESC);
CREATE INDEX IF NOT EXISTS idx_video_comment_datetime_heat ON video_comment_table (datetime, heat DESC);
CREATE INDEX IF NOT EXISTS idx_video_comment_bvid ON video_comment_table (bvid);

-- Dynamics comment table indexes
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_datetime ON dynamics_comment_table (datetime);
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_like_num ON dynamics_comment_table (like_num DESC);
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_heat ON dynamics_comment_table (heat DESC);
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_datetime_heat ON dynamics_comment_table (datetime, heat DESC);
CREATE INDEX IF NOT EXISTS idx_dynamics_comment_oid ON dynamics_comment_table (oid);
```

**Benefits**:
- Faster WHERE clause filtering on datetime
- Optimized ORDER BY operations on heat/like_num
- Improved JOIN performance

### 5. Reduced String Processing
- **Change**: String cleaning (regex operations) only performed on final top N results
- **Benefit**: Eliminates unnecessary processing of comments that won't be returned
- **Impact**: Reduced CPU usage for large datasets

## Performance Improvements

### Expected Performance Gains

1. **Query Time Reduction**: 60-80% faster database queries
   - Single query vs multiple queries
   - Database-level sorting vs Python sorting
   - Proper indexes vs full table scans

2. **Memory Usage Reduction**: 70-90% less memory usage
   - Only top N comments loaded vs all comments
   - Eliminated large Python lists

3. **Cache Performance**: 95%+ faster for cached requests
   - Sub-10ms response times for cached data
   - Reduced database load

4. **Scalability**: Better performance with larger datasets
   - Performance scales with result size (N) rather than total comment count
   - Consistent response times regardless of date range size

### Benchmark Targets

- **Excellent**: < 100ms response time
- **Good**: < 500ms response time  
- **Acceptable**: < 1s response time

## Testing and Verification

### Performance Test Script
Run the performance test to measure improvements:

```bash
cd backend
python performance_test_top_comments.py
```

### Database Migration
Apply performance indexes to existing tables:

```bash
cd backend/migrations
python add_comment_performance_indexes.py
```

## API Contract Preservation

The optimizations maintain 100% backward compatibility:

- **Same endpoint**: `/comment/top_n`
- **Same parameters**: `s`, `e`, `n`, `source`, `vtuber`
- **Same response format**: Identical JSON structure
- **Same functionality**: All features preserved

## Monitoring and Maintenance

### Cache Management
- Automatic cache cleanup when size exceeds 100 entries
- 5-minute TTL prevents stale data
- Cache keys include all query parameters for accuracy

### Index Maintenance
- Indexes are created with `IF NOT EXISTS` to prevent conflicts
- Regular database maintenance should include index statistics updates
- Monitor index usage with PostgreSQL query statistics

### Performance Monitoring
- Log query execution times
- Monitor cache hit rates
- Track memory usage patterns

## Future Optimizations

### Potential Enhancements
1. **Redis Caching**: Replace in-memory cache with Redis for distributed caching
2. **Query Result Pagination**: Add pagination for very large result sets
3. **Materialized Views**: Pre-computed top comments for popular time ranges
4. **Connection Pooling**: Optimize database connection management
5. **Async Batch Processing**: Background processing for cache warming

### Monitoring Recommendations
1. Set up alerts for response times > 1s
2. Monitor cache hit rates (target: >70%)
3. Track database query performance
4. Monitor memory usage trends
