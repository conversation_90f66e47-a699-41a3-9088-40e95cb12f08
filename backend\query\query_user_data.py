import json
import re
import hashlib
import os
from collections import defaultdict
from datetime import datetime, timedelta
from statistics import mean

import asyncpg
import psycopg2
from click import Choice

import utils as U
from algos.base.dataclasses import VupRencentInfo
from backend.query.query_live_info import query_now_live_info_by_room
from utils import word_cloud_gen
from sql.db_pool import get_connection
from const import PROJECT_ROOT
from logger import logger
from sql import user_sql as fatal_sql

# A refractory of functions for querying user data, for fetch_user_data's pgsql version.

# Simple in-memory cache for top comments
_top_comments_cache = {}
_cache_ttl_seconds = 300  # 5 minutes cache TTL

# Simple in-memory cache for top commenting users
_top_users_cache = {}
_users_cache_ttl_seconds = 300  # 5 minutes cache TTL

# Word cloud cache TTL (24 hours for file-based caching)
_wordcloud_cache_ttl_seconds = 86400  # 24 hours cache TTL


async def query_current_stat_by_mid(uid: str):
    """
    查询指定uid主播的最新数据。
    Query the latest data for a specific user ID from the current_stat_table.

    Args:
        uid: 用户 UID。

    Returns:
        asyncpg.Record or None: 包含最新统计数据的记录，如果查询失败或无数据则返回 None。
    """
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT *
                FROM current_stat_table
                WHERE uid = $1
                ORDER BY datetime DESC
                LIMIT 1
            """
            # Use $1, $2, etc. for placeholders with asyncpg
            result = await conn.fetchrow(sql_query, uid)

            if result:
                logger.info(f"成功查询到 UID={uid} 的最新数据")
                return result
            else:
                logger.info(f"数据库中未找到 UID={uid} 的数据")
                return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询 UID={uid} 的最新数据时出错: {e}")
        return None
    except Exception as ex:
        logger.error(f"查询 UID={uid} 的最新数据时发生意外错误: {ex}")
        return None


async def query_peroid_user_all_stat_by_uid_and_time(
    mid, start_time: str, end_time: str
):
    """
    查询用户在指定时间范围内的所有统计数据。
    Query for user's all stats by mid and recent days.
    Args:
        mid: 用户 UID (vup uid)。
        recent: 最近的天数。如果为 -1，则获取所有记录。

    Returns:
        List[List]: 包含统计数据的列表，格式为
                    [[datetime_str, video_total_num, article_total_num, likes_total_num, elec_num, follower_num, dahanghai_num], ...]
                    如果查询失败或无数据，则返回空列表。
    """
    try:
        start_date = datetime.strptime(start_time, "%Y-%m-%d").date()
        end_date = datetime.strptime(end_time, "%Y-%m-%d").date()
        async with get_connection() as conn:
            base_sql = """
                SELECT datetime, video_total_num, article_total_num, likes_total_num, elec_num, follower_num, dahanghai_num
                FROM current_stat_table
                WHERE uid = $1
            """
            params = [mid]
            if start_time and end_time:
                sql_query = (
                    base_sql
                    + " AND datetime::date >= $2 AND datetime::date <= $3 ORDER BY datetime ASC"
                )
                params.extend([start_date, end_date])
            else:
                sql_query = base_sql + " ORDER BY datetime ASC"

            # asyncpg uses conn.fetch for multiple rows
            results = await conn.fetch(sql_query, *params)

            if results:
                formatted_data = [
                    [
                        row["datetime"].strftime("%Y%m%d%H"),
                        row["video_total_num"],
                        row["article_total_num"],
                        row["likes_total_num"],
                        row["elec_num"],
                        row["follower_num"],
                        row["dahanghai_num"],
                    ]
                    for row in results
                ]
                return formatted_data
            else:
                return []

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询用户全部统计数据时出错 (query_whole_user_all_stat_by_room_and_recent): {e}"
        )
        return []
    except Exception as ex:
        logger.error(f"查询用户全部统计数据时发生意外错误: {ex}")
        return []


async def query_whole_user_all_stat_by_uid_and_recent(mid, recent: int = -1, limit: int = None):
    """
    查询用户在指定时间范围内的所有统计数据。
    Query for user's all stats by mid and recent days.

    Args:
        mid: 用户 UID (vup uid)。
        recent: 最近的天数。如果为 -1，则获取所有记录。
        limit: 最大返回记录数。如果为 None，当 recent=-1 时默认限制为 1000 条记录。

    Returns:
        List[List]: 包含统计数据的列表，格式为
                    [[datetime_str, video_total_num, article_total_num, likes_total_num, elec_num, follower_num, dahanghai_num], ...]
                    如果查询失败或无数据，则返回空列表。
    """
    try:
        async with get_connection() as conn:
            # Optimized SQL with proper formatting in database
            base_sql = """
                SELECT
                    TO_CHAR(datetime, 'YYYYMMDDHH24') as datetime_str,
                    video_total_num,
                    article_total_num,
                    likes_total_num,
                    elec_num,
                    follower_num,
                    dahanghai_num
                FROM current_stat_table
                WHERE uid = $1
            """
            params = [mid]
            param_count = 1

            if recent != -1:
                days_before_time = datetime.now() - timedelta(days=recent)
                param_count += 1
                sql_query = base_sql + f" AND datetime >= ${param_count}"
                params.append(days_before_time)
            else:
                sql_query = base_sql

            # Add ordering - DESC for recent data first, then limit
            sql_query += " ORDER BY datetime DESC"

            # Apply limit - default to 1000 for recent=-1 to prevent timeouts
            if limit is not None:
                param_count += 1
                sql_query += f" LIMIT ${param_count}"
                params.append(limit)
            elif recent == -1:
                # Default limit for all data to prevent timeouts
                param_count += 1
                sql_query += f" LIMIT ${param_count}"
                params.append(1000)

            # asyncpg uses conn.fetch for multiple rows
            results = await conn.fetch(sql_query, *params)

            if results:
                # Data is already formatted in SQL, just convert to list format
                formatted_data = [
                    [
                        row["datetime_str"],
                        row["video_total_num"],
                        row["article_total_num"],
                        row["likes_total_num"],
                        row["elec_num"],
                        row["follower_num"],
                        row["dahanghai_num"],
                    ]
                    for row in results
                ]
                # Reverse to get chronological order (oldest first) as expected by API
                return list(reversed(formatted_data))
            else:
                return []

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询用户全部统计数据时出错 (query_whole_user_all_stat_by_uid_and_recent): {e}"
        )
        return []
    except Exception as ex:
        logger.error(f"查询用户全部统计数据时发生意外错误: {ex}")
        return []


async def query_whole_user_follower_num_by_mid_and_recent(mid, recent: int = -1):
    """
    查询用户在指定时间范围内的粉丝数历史记录。
    Query for user's follower number history by mid and recent days.

    Args:
        mid: 用户 UID。
        recent: 最近的天数。如果为 -1，则获取所有记录。

    Returns:
        List[List]: 包含粉丝数历史的列表，格式为 [[datetime_str, follower_num], ...]
                    如果查询失败或无数据，则返回空列表。
    """
    try:
        async with get_connection() as conn:
            base_sql = """
                SELECT datetime, follower_num
                FROM current_stat_table
                WHERE uid = $1 AND follower_num IS NOT NULL
            """
            params = [mid]

            if recent != -1:
                days_before_time = datetime.now() - timedelta(days=recent)
                sql_query = base_sql + " AND datetime >= $2 ORDER BY datetime DESC"
                params.append(days_before_time)
            else:
                sql_query = base_sql + " ORDER BY datetime DESC"

            results = await conn.fetch(sql_query, *params)

            if results:
                formatted_data = [
                    [row["datetime"].strftime("%Y%m%d%H"), row["follower_num"]]
                    for row in results
                ]
                return formatted_data
            else:
                return []

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询用户粉丝数历史时出错 (query_whole_user_follower_num_by_mid_and_recent): {e}"
        )
        return []
    except Exception as ex:
        logger.error(f"查询用户粉丝数历史时发生意外错误: {ex}")
        return []


async def query_now_user_follower_num_by_mid(mid):
    """
    查询用户当前粉丝数。
    Query user's current follower number.

    Args:
        mid: 用户 UID。

    Returns:
        int: 当前粉丝数。如果查询失败，则返回 -1。
    """
    # TODO: 查询当前此小时的粉丝数，若无：返回最新的，并logwaring
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT follower_num
                FROM current_stat_table
                WHERE uid = $1 AND follower_num IS NOT NULL
                ORDER BY datetime DESC
                LIMIT 1
            """
            result = await conn.fetchval(sql_query, mid)

            if result is not None:  # fetchval returns None if no row or value is NULL
                return result
            else:
                # Check if the table was empty or follower_num was NULL
                # For simplicity, returning -1 if no specific value found
                return -1
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询用户当前粉丝数时出错 (query_now_user_follower_num_by_mid): {e}"
        )
        return -1
    except Exception as ex:
        logger.error(f"查询用户当前粉丝数时发生意外错误: {ex}")
        return -1


async def query_whole_dahanghai_num_by_mid_and_recent(mid, recent: int = -1):
    """
    查询用户在指定时间范围内的大航海数历史记录。
    Query for user's dahanghai number history by mid and recent days.

    Args:
        mid: 用户 UID。
        recent: 最近的天数。如果为 -1，则获取所有记录。

    Returns:
        List[List]: 包含大航海数历史的列表，格式为 [[datetime_str, dahanghai_num], ...]
                    如果查询失败或无数据，则返回空列表。
    """
    try:
        async with get_connection() as conn:
            base_sql = """
                SELECT datetime, dahanghai_num
                FROM current_stat_table
                WHERE uid = $1 AND dahanghai_num IS NOT NULL
            """
            params = [mid]

            if recent != -1:
                days_before_time = datetime.now() - timedelta(days=recent)
                sql_query = base_sql + " AND datetime >= $2 ORDER BY datetime DESC"
                params.append(days_before_time)
            else:
                sql_query = base_sql + " ORDER BY datetime DESC"

            results = await conn.fetch(sql_query, *params)

            if results:
                formatted_data = [
                    [row["datetime"].strftime("%Y%m%d%H"), row["dahanghai_num"]]
                    for row in results
                ]
                return formatted_data
            else:
                return []

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询用户大航海数历史时出错 (query_whole_dahanghai_num_by_mid_and_recent): {e}"
        )
        return []
    except Exception as ex:
        logger.error(f"查询用户大航海数历史时发生意外错误: {ex}")
        return []


async def query_now_user_dahanghai_num_by_mid(mid):
    """
    查询用户当前大航海数。
    Query user's current dahanghai number.

    Args:
        mid: 用户 UID。

    Returns:
        int: 当前大航海数。如果查询失败，则返回 -1。
    """
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT dahanghai_num
                FROM current_stat_table
                WHERE uid = $1 AND dahanghai_num IS NOT NULL
                ORDER BY datetime DESC
                LIMIT 1
            """
            result = await conn.fetchval(sql_query, mid)

            if result is not None:
                return result
            else:
                return -1
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询用户当前大航海数时出错 (query_now_user_dahanghai_num_by_mid): {e}"
        )
        return -1
    except Exception as ex:
        logger.error(f"查询用户当前大航海数时发生意外错误: {ex}")
        return -1


async def query_user_info_by_mid(mid):
    """
    根据 UID 查询用户的基本信息。
    Query user's basic information by mid.

    Args:
        mid: 用户 UID。

    Returns:
        asyncpg.Record or None: 包含用户信息的记录，如果查询失败或用户不存在，则返回 None。
    """
    try:
        async with get_connection() as conn:
            sql_query = "SELECT uid, name, face, sign, birthday, top_photo, room_id, live_url FROM user_info_table WHERE uid = $1"
            user_data = await conn.fetchrow(sql_query, mid)

            if user_data:
                return user_data
            else:
                return None

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询用户信息时出错 (query_user_info_by_mid): {e}")
        return None
    except Exception as ex:
        logger.error(f"查询用户信息时发生意外错误: {ex}")
        return None


async def calculate_dahanghai_rate_by_mid(mid, recent: int = 90):
    """
    计算指定用户的大航海增长率。
    Calculate the dahanghai growth rate for a given user.

    Args:
        mid: 用户 UID。
        recent: 计算增长率的时间窗口（天数）。

    Returns:
        str: 格式化的增长率字符串 (e.g., "10.5%") 或 "N/A" 或 "Error"。
    """
    whole_dahanghai_num_raw = await query_whole_dahanghai_num_by_mid_and_recent(
        mid, recent=recent + 7
    )

    if not whole_dahanghai_num_raw or len(whole_dahanghai_num_raw) < 2:
        logger.warning(
            f"计算 mid={mid} 大航海增长率所需数据不足，尝试获取更长时间范围的数据..."
        )
        whole_dahanghai_num_raw = await query_whole_dahanghai_num_by_mid_and_recent(
            mid, recent=365
        )
        if not whole_dahanghai_num_raw or len(whole_dahanghai_num_raw) < 2:
            logger.error(f"无法计算 mid={mid} 大航海增长率，数据严重不足")
            return "N/A"

    data = []
    for row_data in whole_dahanghai_num_raw:
        try:
            time_dt = datetime.strptime(row_data[0], "%Y%m%d%H")
            num_int = U.safe_int(row_data[1])
            if num_int is not None:
                data.append((time_dt, num_int))
        except (ValueError, TypeError, IndexError):
            logger.warning(f"跳过无效的大航海数据行: {row_data}")
            continue

    if not data:
        logger.error(f"处理后无有效大航海数据用于计算 mid={mid} 增长率")
        return "N/A"

    data.sort(key=lambda x: x[0])

    latest_time, latest_num = data[-1]

    target_past_time = latest_time - timedelta(days=recent)
    days_before_num = None
    closest_diff = timedelta.max

    for time_dt, num_int in reversed(data[:-1]):
        if time_dt <= target_past_time:
            diff = target_past_time - time_dt
            if diff < closest_diff:
                closest_diff = diff
                days_before_num = num_int
            if diff == timedelta(0):
                break

    if days_before_num is None or days_before_num == 0:
        logger.warning(
            f"未找到 mid={mid} 在 {recent} 天前的有效大航海记录或记录为0，无法计算增长率"
        )
        return "N/A"

    try:
        rate = (latest_num - days_before_num) / days_before_num
        return f"{rate * 100:.1f}%"
    except ZeroDivisionError:
        logger.error(
            f"计算 mid={mid} 大航海增长率时发生除零错误 (days_before_num: {days_before_num})"
        )
        return "Error"


async def calculate_follower_rate_by_mid(mid, recent: int = 90):
    """
    计算指定用户的粉丝增长率。
    Calculate the follower growth rate for a given user.

    Args:
        mid: 用户 UID。
        recent: 计算增长率的时间窗口（天数）。

    Returns:
        str: 格式化的增长率字符串 (e.g., "10.5%") 或 "N/A" 或 "Error"。
    """
    whole_follower_num_raw = await query_whole_user_follower_num_by_mid_and_recent(
        mid, recent=recent + 7
    )

    if not whole_follower_num_raw or len(whole_follower_num_raw) < 2:
        logger.warning(
            f"计算 mid={mid} 粉丝增长率所需数据不足，尝试获取更长时间范围的数据..."
        )
        whole_follower_num_raw = await query_whole_user_follower_num_by_mid_and_recent(
            mid, recent=365
        )
        if not whole_follower_num_raw or len(whole_follower_num_raw) < 2:
            logger.error(f"无法计算 mid={mid} 粉丝增长率，数据严重不足")
            return "N/A"

    data = []
    for row_data in whole_follower_num_raw:  # Renamed 'row' to 'row_data'
        try:
            time_dt = datetime.strptime(row_data[0], "%Y%m%d%H")
            num_int = U.safe_int(row_data[1])
            if num_int is not None:
                data.append((time_dt, num_int))
        except (ValueError, TypeError, IndexError):
            logger.warning(f"跳过无效的粉丝数据行: {row_data}")
            continue

    if not data:
        logger.error(f"处理后无有效粉丝数据用于计算 mid={mid} 增长率")
        return "N/A"

    data.sort(key=lambda x: x[0])

    latest_time, latest_num = data[-1]

    target_past_time = latest_time - timedelta(days=recent)
    days_before_num = None
    closest_diff = timedelta.max

    for time_dt, num_int in reversed(data[:-1]):
        if time_dt <= target_past_time:
            diff = target_past_time - time_dt
            if diff < closest_diff:
                closest_diff = diff
                days_before_num = num_int
            if diff == timedelta(0):
                break

    if days_before_num is None or days_before_num == 0:
        logger.warning(
            f"未找到 mid={mid} 在 {recent} 天前的有效粉丝记录或记录为0，无法计算增长率"
        )
        return "N/A"

    try:
        rate = (latest_num - days_before_num) / days_before_num
        return f"{rate * 100:.1f}%"
    except ZeroDivisionError:
        logger.error(
            f"计算 mid={mid} 粉丝增长率时发生除零错误 (days_before_num: {days_before_num})"
        )
        return "Error"


async def query_user_dynamics_by_mid(mid):
    """
    从数据库查询指定用户的所有动态。
    Query all dynamics for a given user from the database.

    Args:
        mid: 用户 UID。

    Returns:
        List[]: 包含动态信息的列表：
                    [name, timestamp, datetime, dynamic_content, url, topic, dynamic_id, share_num, comment_num, like_num, comment_id, comment_type, heat]
                    如果查询失败或无数据，则返回空列表。
    """
    dynamics_with_heat = []
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT name, timestamp, datetime, dynamic_content, url, topic, dynamic_id, share_num, comment_num, like_num, comment_id, comment_type, heat
                FROM dynamics_table
                WHERE uid = $1
                ORDER BY datetime DESC
            """
            results = await conn.fetch(sql_query, mid)

            for row in results:
                try:
                    share_num = U.safe_int(row["share_num"])
                    comment_num = U.safe_int(row["comment_num"])
                    like_num = U.safe_int(row["like_num"])

                    heat = round(
                        U.dynamic_calculate_hotness(share_num, comment_num, like_num),
                        2,
                    )

                    content = row["dynamic_content"]
                    if content:
                        cleaned_content = re.sub(r"\[.*?\]", "", content).strip()
                        if cleaned_content:
                            dynamics_with_heat.append(
                                {
                                    "id": row["comment_id"],
                                    "name": row["name"],
                                    "content": cleaned_content.replace("\n", " "),
                                    "topic": row["topic"],
                                    "url": row["url"],
                                    "heat": heat,
                                    "pubtime": (
                                        row["datetime"].strftime("%Y-%m-%d")
                                        if row["datetime"]
                                        else None
                                    ),
                                    "share_num": share_num,
                                    "comment_num": comment_num,
                                    "like_num": like_num,
                                }
                            )
                except Exception as e:
                    logger.error(
                        f"处理动态 {row.get('dynamic_id', 'N/A')} 时出错: {e}"
                    )  # Changed ID to dynamic_id
                    continue
            return dynamics_with_heat
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询用户动态时出错 (query_user_dynamics_by_mid): {e}")
        return []
    except Exception as ex:
        logger.error(f"查询用户动态时发生意外错误: {ex}")
        return []


async def query_all_video_list_by_mid(mid):
    """
    从数据库查询指定用户的所有视频列表。
    Query all video list for a given user from the database.

    Args:
        mid: 用户 UID。

    Returns:
        List[List]: 包含视频信息的列表，格式类似原 CSV：
                    [name, bvid, timestamp, datetime, video_name, description, cover, play_num, comment_num, like_num, coin, favorite_num, share_num, danmuku_num, aid, length, honor_short, honor_count, honor, video_ai_conclusion, heat]
                    如果查询失败或无数据，则返回空列表。
    """
    videos_with_heat = []
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT name, bvid, timestamp, datetime, video_name, description, cover, play_num, comment_num, like_num, coin, favorite_num, share_num, danmuku_num, aid, length, honor_short, honor_count, honor, video_ai_conclusion, heat
                FROM videos_table
                WHERE uid = $1
                ORDER BY datetime DESC
            """
            results = await conn.fetch(sql_query, mid)

            if not results:
                logger.info(f"在指定时间范围内未找到 mid={mid} 的视频")
                return []

            for row in results:
                try:
                    play_num = U.safe_int(row["play_num"])
                    comment_num = U.safe_int(row["comment_num"])
                    honor_count = U.safe_int(row["honor_count"])
                    like_num = U.safe_int(row["like_num"])
                    coin_num = U.safe_int(row["coin"])
                    favorite_num = U.safe_int(row["favorite_num"])

                    heat = round(
                        U.video_calculate_hotness(
                            play_num,
                            comment_num,
                            honor_count,
                            like_num,
                            coin_num,
                            favorite_num,
                        ),
                        2,
                    )

                    videos_with_heat.append(
                        {
                            "bvid": row["bvid"],
                            "name": row["video_name"],
                            "description": row["description"],
                            "face": row["cover"],
                            "heat": heat,
                            "play_num": play_num,
                            "comment_num": comment_num,
                            "like_num": like_num,
                            "coin_num": coin_num,
                            "favorite_num": favorite_num,
                            "danmaku_num": row["danmuku_num"],
                            "share_num": row["share_num"],
                            "aid": row["aid"],
                            "length": row["length"],
                            "pubtime": (
                                row["datetime"].strftime("%Y-%m-%d")
                                if row["datetime"]
                                else None
                            ),
                            "honor_short": (
                                str(row["honor_short"]) if row["honor_short"] else ""
                            ),
                            "honor": row["honor"],
                            "honor_count": honor_count,
                            "video_ai_conclusion": row["video_ai_conclusion"],
                        }
                    )
                except Exception as e:
                    logger.error(f"计算视频 {row.get('bvid', 'N/A')} 热度时出错: {e}")
                    continue
            return videos_with_heat
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询用户视频列表时出错 (query_all_video_list_by_mid): {e}")
        return []
    except Exception as ex:
        logger.error(f"查询用户视频列表时发生意外错误: {ex}")
        return []


async def query_top_n_comments(mid, start_time_str, end_time_str, n, source="video"):
    """
    从数据库获取指定用户在指定时间范围内热度（点赞数）最高的 n 条评论（视频+动态）。
    Query top n comments (video + dynamic) by heat (likes) for a given user within a time range.

    OPTIMIZED VERSION: Uses single database query with ORDER BY and LIMIT for better performance.
    Includes caching for frequently requested data.

    Args:
        mid: 用户 UID。
        start_time_str: 开始时间字符串 (YYYY-MM-DD)。
        end_time_str: 结束时间字符串 (YYYY-MM-DD)。
        n: 需要获取的评论数量。
        source: 数据来源 ("video", "dynamic", "all")

    Returns:
        List[Dict]: 包含评论信息的字典列表，按热度降序排列。
                    每个字典包含: 'name', 'uid', 'face', 'time', 'comment', 'heat', 'from_id', 'from_name'
                    如果查询失败或无数据，则返回空列表。
    """
    # Create cache key
    cache_key = hashlib.md5(f"{mid}_{start_time_str}_{end_time_str}_{n}_{source}".encode()).hexdigest()

    # Check cache first
    current_time = datetime.now()
    if cache_key in _top_comments_cache:
        cached_data, cached_time = _top_comments_cache[cache_key]
        if (current_time - cached_time).total_seconds() < _cache_ttl_seconds:
            logger.info(f"返回缓存的热门评论数据 (mid={mid}, cache_key={cache_key[:8]})")
            return cached_data

    try:
        start_time_dt = datetime.strptime(start_time_str, "%Y-%m-%d")
        end_time_dt = datetime.strptime(end_time_str, "%Y-%m-%d") + timedelta(days=1)
    except ValueError:
        logger.error("无效的日期格式，请使用 'YYYY-MM-DD'")
        return []

    try:
        async with get_connection() as conn:
            # Build optimized query with UNION ALL and database-level sorting
            query_parts = []
            params = []
            param_index = 1

            # Video comments query part
            if source == "video" or source == "all":
                video_comment_table_name = fatal_sql.get_video_comment_table_name(mid)
                if video_comment_table_name:
                    video_query = f"""
                        SELECT
                            vc.uname as name,
                            vc.mid as uid,
                            vc.face,
                            vc.datetime,
                            vc.comment,
                            vc.like_num as heat,
                            vc.bvid as from_id,
                            vc.from_video_title as from_name,
                            'video' as source
                        FROM "{video_comment_table_name}" vc
                        JOIN videos_table v ON vc.bvid = v.bvid
                        WHERE v.uid = ${param_index} AND vc.datetime >= ${param_index + 1} AND vc.datetime < ${param_index + 2}
                    """
                    query_parts.append(video_query)
                    params.extend([mid, start_time_dt, end_time_dt])
                    param_index += 3

            # Dynamic comments query part
            if source == "dynamic" or source == "all":
                dynamic_comment_table_name = fatal_sql.get_dynamics_comment_table_name(mid)
                if dynamic_comment_table_name:
                    dynamic_query = f"""
                        SELECT
                            dc.uname as name,
                            dc.mid as uid,
                            dc.face,
                            dc.datetime,
                            dc.comment,
                            dc.like_num as heat,
                            dc.oid as from_id,
                            dc.from_dynamic as from_name,
                            'dynamic' as source
                        FROM "{dynamic_comment_table_name}" dc
                        JOIN dynamics_table d ON dc.oid = d.dynamic_id
                        WHERE d.uid = ${param_index} AND dc.datetime >= ${param_index + 1} AND dc.datetime < ${param_index + 2}
                    """
                    query_parts.append(dynamic_query)
                    params.extend([mid, start_time_dt, end_time_dt])

            if not query_parts:
                logger.warning(f"No valid comment tables found for mid={mid}")
                return []

            # Combine queries with UNION ALL and add ORDER BY + LIMIT
            combined_query = " UNION ALL ".join(query_parts)
            final_query = f"""
                SELECT * FROM ({combined_query}) as combined_comments
                ORDER BY heat DESC
                LIMIT {n}
            """

            # Execute optimized query
            results = await conn.fetch(final_query, *params)

            if not results:
                logger.warning(f"在指定时间范围内未找到 mid={mid} 的任何评论")
                return []

            # Process only the top N results (much more efficient)
            top_comments = []
            for row in results:
                # Clean dynamic from_name only when needed
                from_name = row["from_name"]
                if row["source"] == "dynamic" and from_name:
                    from_name = re.sub(r"\[.*?\]", "", from_name).strip()[:50] or from_name

                top_comments.append({
                    "name": row["name"],
                    "uid": row["uid"],
                    "face": row["face"],
                    "time": (
                        row["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                        if row["datetime"]
                        else None
                    ),
                    "comment": row["comment"],
                    "heat": U.safe_int(row["heat"]),
                    "from_id": row["from_id"],
                    "from_name": from_name,
                    "source": row["source"],
                })

            logger.info(f"获取到 {len(top_comments)} 条热门评论 (mid={mid}, source={source})")

            # Cache the results
            _top_comments_cache[cache_key] = (top_comments, current_time)

            # Clean old cache entries (simple cleanup)
            if len(_top_comments_cache) > 100:  # Keep cache size reasonable
                oldest_key = min(_top_comments_cache.keys(),
                               key=lambda k: _top_comments_cache[k][1])
                del _top_comments_cache[oldest_key]

            return top_comments

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询热门评论时出错 (query_top_n_comments, mid={mid}): {e}")
        return []
    except Exception as ex:
        logger.error(f"查询热门评论时发生意外错误 (mid={mid}): {ex}")
        return []


async def query_top_n_comments_user(
    mid, start_time_str, end_time_str, n, source: str = "all"
):
    """
    从数据库获取指定用户在指定时间范围内评论活跃用户（按点赞数和评论数）。
    Query top n active commenting users by likes and comment count for a given user within a time range.

    OPTIMIZED VERSION: Uses single database query with GROUP BY and ORDER BY for better performance.
    Includes caching for frequently requested data.

    Args:
        mid: 用户 UID (主播的 UID)。
        start_time_str: 开始时间字符串 (YYYY-MM-DD)。
        end_time_str: 结束时间字符串 (YYYY-MM-DD)。
        n: 需要获取的用户数量。
        source: 数据来源，可选值为 "video" 或 "dynamic" 或 "all"。

    Returns:
        Tuple[List[Dict], List[Dict], List[Dict]]: 包含三个列表的元组。
            第一个列表按总点赞数降序排列: [{'name', 'likeNum', 'uid', 'face'}, ...]
            第二个列表按评论数降序排列: [{'name', 'appealNum', 'uid', 'face'}, ...]
            第三个列表按回复数降序排列: [{'name', 'rcountSum', 'uid', 'face'}, ...]
    """
    # Create cache key
    cache_key = hashlib.md5(f"users_{mid}_{start_time_str}_{end_time_str}_{n}_{source}".encode()).hexdigest()

    # Check cache first
    current_time = datetime.now()
    if cache_key in _top_users_cache:
        cached_data, cached_time = _top_users_cache[cache_key]
        if (current_time - cached_time).total_seconds() < _users_cache_ttl_seconds:
            logger.info(f"返回缓存的活跃用户数据 (mid={mid}, cache_key={cache_key[:8]})")
            return cached_data

    try:
        start_time_dt = datetime.strptime(start_time_str, "%Y-%m-%d")
        end_time_dt = datetime.strptime(end_time_str, "%Y-%m-%d") + timedelta(days=1)
    except ValueError:
        logger.error("无效的日期格式，请使用 'YYYY-MM-DD'")
        return [], [], []

    try:
        async with get_connection() as conn:
            # Build optimized query with UNION ALL and database-level aggregation
            query_parts = []
            params = []
            param_index = 1

            # Video comments query part
            if source == "video" or source == "all":
                video_comment_table_name = fatal_sql.get_video_comment_table_name(mid)
                if video_comment_table_name:
                    video_query = f"""
                        SELECT
                            vc.mid as uid,
                            vc.uname as name,
                            vc.face,
                            vc.like_num,
                            vc.rcount,
                            1 as comment_count
                        FROM "{video_comment_table_name}" vc
                        JOIN videos_table v ON vc.bvid = v.bvid
                        WHERE v.uid = ${param_index} AND vc.datetime >= ${param_index + 1} AND vc.datetime < ${param_index + 2}
                              AND vc.mid IS NOT NULL AND vc.mid != ${param_index}
                    """
                    query_parts.append(video_query)
                    params.extend([mid, start_time_dt, end_time_dt, mid])
                    param_index += 4

            # Dynamic comments query part
            if source == "dynamic" or source == "all":
                dynamics_comment_table_name = fatal_sql.get_dynamics_comment_table_name(mid)
                if dynamics_comment_table_name:
                    dynamic_query = f"""
                        SELECT
                            dc.mid as uid,
                            dc.uname as name,
                            dc.face,
                            dc.like_num,
                            dc.rcount,
                            1 as comment_count
                        FROM "{dynamics_comment_table_name}" dc
                        JOIN dynamics_table d ON dc.oid = d.dynamic_id
                        WHERE d.uid = ${param_index} AND dc.datetime >= ${param_index + 1} AND dc.datetime < ${param_index + 2}
                              AND dc.mid IS NOT NULL AND dc.mid != ${param_index}
                    """
                    query_parts.append(dynamic_query)
                    params.extend([mid, start_time_dt, end_time_dt, mid])

            if not query_parts:
                logger.warning(f"No valid comment tables found for mid={mid}")
                return [], [], []

            # Combine queries with UNION ALL and add GROUP BY aggregation
            combined_query = " UNION ALL ".join(query_parts)
            aggregated_query = f"""
                SELECT
                    name,
                    uid,
                    face,
                    SUM(like_num) as total_likes,
                    SUM(comment_count) as total_comments,
                    SUM(rcount) as total_replies
                FROM ({combined_query}) as combined_comments
                GROUP BY name, uid, face
            """

            # Execute optimized aggregated query
            results = await conn.fetch(aggregated_query, *params)

            if not results:
                logger.warning(f"在指定时间范围内未找到 mid={mid} 的任何用户评论")
                return [], [], []

            # Create separate queries for each ranking with database-level sorting and limiting
            # Top likes users
            top_likes_query = f"""
                SELECT name, uid, face, total_likes as likeNum
                FROM ({aggregated_query}) as user_stats
                ORDER BY total_likes DESC
                LIMIT {n}
            """
            top_likes_results = await conn.fetch(top_likes_query, *params)
            top_likes_list = [
                {
                    "name": row["name"],
                    "likeNum": U.safe_int(row["likenum"]),
                    "uid": row["uid"],
                    "face": row["face"],
                }
                for row in top_likes_results
            ]

            # Top comments users
            top_comments_query = f"""
                SELECT name, uid, face, total_comments as appealNum
                FROM ({aggregated_query}) as user_stats
                ORDER BY total_comments DESC
                LIMIT {n}
            """
            top_comments_results = await conn.fetch(top_comments_query, *params)
            top_comments_list = [
                {
                    "name": row["name"],
                    "appealNum": U.safe_int(row["appealnum"]),
                    "uid": row["uid"],
                    "face": row["face"],
                }
                for row in top_comments_results
            ]

            # Top replies users
            top_replies_query = f"""
                SELECT name, uid, face, total_replies as rcountSum
                FROM ({aggregated_query}) as user_stats
                ORDER BY total_replies DESC
                LIMIT {n}
            """
            top_replies_results = await conn.fetch(top_replies_query, *params)
            top_replies_list = [
                {
                    "name": row["name"],
                    "rcountSum": U.safe_int(row["rcountsum"]),
                    "uid": row["uid"],
                    "face": row["face"],
                }
                for row in top_replies_results
            ]

            logger.info(f"获取到活跃用户数据 (mid={mid}, source={source}): "
                       f"likes={len(top_likes_list)}, comments={len(top_comments_list)}, replies={len(top_replies_list)}")

            # Cache the results
            result_tuple = (top_likes_list, top_comments_list, top_replies_list)
            _top_users_cache[cache_key] = (result_tuple, current_time)

            # Clean old cache entries (simple cleanup)
            if len(_top_users_cache) > 100:  # Keep cache size reasonable
                oldest_key = min(_top_users_cache.keys(),
                               key=lambda k: _top_users_cache[k][1])
                del _top_users_cache[oldest_key]

            return result_tuple

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询活跃评论用户时出错 (query_top_n_comments_user, mid={mid}): {e}"
        )
        return [], [], []
    except Exception as ex:
        logger.error(f"查询活跃评论用户时发生意外错误 (mid={mid}): {ex}")
        return [], [], []


async def query_top_n_videos(mid, start_time_str, end_time_str, n):
    """
    从数据库获取指定用户在指定时间范围内热度最高的 n 个视频。
    Query top n videos by heat for a given user within a time range.

    Args:
        mid: 用户 UID。
        start_time_str: 开始时间字符串 (YYYY-MM-DD)。
        end_time_str: 结束时间字符串 (YYYY-MM-DD)。
        n: 需要获取的视频数量。

    Returns:
        List[Dict]: 包含视频信息的字典列表，按热度降序排列。
                    每个字典包含: 'bvid', 'name', 'face' (cover), 'heat', 'pubtime', 'honor_short', 'honor_count'
                    如果查询失败或无数据，则返回空列表。
    """
    try:
        start_time_dt = datetime.strptime(start_time_str, "%Y-%m-%d")
        end_time_dt = datetime.strptime(end_time_str, "%Y-%m-%d") + timedelta(days=1)
    except ValueError:
        logger.error("无效的日期格式，请使用 'YYYY-MM-DD'")
        return []

    videos_with_heat = []
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT bvid, video_name, cover, datetime, play_num, comment_num, like_num, coin, favorite_num, honor_count, honor_short
                FROM videos_table
                WHERE uid = $1 AND datetime >= $2 AND datetime < $3
            """
            results = await conn.fetch(sql_query, mid, start_time_dt, end_time_dt)

            if not results:
                logger.info(f"在指定时间范围内未找到 mid={mid} 的视频")
                return []

            for row in results:
                try:
                    play_num = U.safe_int(row["play_num"])
                    comment_num = U.safe_int(row["comment_num"])
                    honor_count = U.safe_int(row["honor_count"])
                    like_num = U.safe_int(row["like_num"])
                    coin_num = U.safe_int(row["coin"])
                    favorite_num = U.safe_int(row["favorite_num"])

                    heat = round(
                        U.video_calculate_hotness(
                            play_num,
                            comment_num,
                            honor_count,
                            like_num,
                            coin_num,
                            favorite_num,
                        ),
                        2,
                    )

                    videos_with_heat.append(
                        {
                            "bvid": row["bvid"],
                            "name": row["video_name"],
                            "description": row["description"],
                            "face": row["cover"],
                            "heat": heat,
                            "play_num": play_num,
                            "comment_num": comment_num,
                            "like_num": like_num,
                            "coin_num": coin_num,
                            "favorite_num": favorite_num,
                            "danmaku_num": row["danmuku_num"],
                            "share_num": row["share_num"],
                            "aid": row["aid"],
                            "length": row["length"],
                            "pubtime": (
                                row["datetime"].strftime("%Y-%m-%d")
                                if row["datetime"]
                                else None
                            ),
                            "honor_short": (
                                str(row["honor_short"]) if row["honor_short"] else ""
                            ),
                            "honor": row["honor"],
                            "honor_count": honor_count,
                            "video_ai_conclusion": row["video_ai_conclusion"],
                        }
                    )
                except Exception as e:
                    logger.error(f"计算视频 {row.get('bvid', 'N/A')} 热度时出错: {e}")
                    continue

        top_n_videos = sorted(videos_with_heat, key=lambda x: x["heat"], reverse=True)[
            :n
        ]
        return top_n_videos

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询热门视频时出错 (query_top_n_videos, mid={mid}): {e}")
        return []
    except Exception as ex:
        logger.error(f"查询热门视频时发生意外错误 (mid={mid}): {ex}")
        return []


async def query_top_n_dynamics(mid, start_time_str, end_time_str, n):
    """
    从数据库获取指定用户在指定时间范围内热度最高的 n 条动态。
    Query top n dynamics by heat for a given user within a time range.

    Args:
        mid: 用户 UID。
        start_time_str: 开始时间字符串 (YYYY-MM-DD)。
        end_time_str: 结束时间字符串 (YYYY-MM-DD)。
        n: 需要获取的动态数量。

    Returns:
        List[Dict]: 包含动态信息的字典列表，按热度降序排列。
                    每个字典包含: 'id' (comment_id), 'name', 'content', 'topic', 'url', 'heat', 'pubtime'
                    如果查询失败或无数据，则返回空列表。
    """
    try:
        start_time_dt = datetime.strptime(start_time_str, "%Y-%m-%d")
        end_time_dt = datetime.strptime(end_time_str, "%Y-%m-%d") + timedelta(days=1)
    except ValueError:
        logger.error("无效的日期格式，请使用 'YYYY-MM-DD'")
        return []

    dynamics_with_heat = []
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT dynamic_id, name, dynamic_content, topic, url, datetime, share_num, comment_num, like_num, comment_id
                FROM dynamics_table
                WHERE uid = $1 AND datetime >= $2 AND datetime < $3
            """
            results = await conn.fetch(sql_query, mid, start_time_dt, end_time_dt)

            if not results:
                logger.info(f"在指定时间范围内未找到 mid={mid} 的动态")
                return []

            for row in results:
                try:
                    share_num = U.safe_int(row["share_num"])
                    comment_num = U.safe_int(row["comment_num"])
                    like_num = U.safe_int(row["like_num"])

                    heat = round(
                        U.dynamic_calculate_hotness(share_num, comment_num, like_num),
                        2,
                    )

                    content = row["dynamic_content"]
                    if content:
                        cleaned_content = re.sub(r"\[.*?\]", "", content).strip()
                        if cleaned_content:
                            dynamics_with_heat.append(
                                {
                                    "id": row["comment_id"],
                                    "name": row["name"],
                                    "content": cleaned_content.replace("\n", " "),
                                    "topic": row["topic"],
                                    "url": row["url"],
                                    "heat": heat,
                                    "pubtime": (
                                        row["datetime"].strftime("%Y-%m-%d")
                                        if row["datetime"]
                                        else None
                                    ),
                                    "share_num": share_num,
                                    "comment_num": comment_num,
                                    "like_num": like_num,
                                }
                            )
                except Exception as e:
                    logger.error(
                        f"处理动态 {row.get('dynamic_id', 'N/A')} 时出错: {e}"
                    )  # Changed ID to dynamic_id
                    continue

        top_n_dynamics = sorted(
            dynamics_with_heat, key=lambda x: x["heat"], reverse=True
        )[:n]
        return top_n_dynamics

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询热门动态时出错 (query_top_n_dynamics, mid={mid}): {e}")
        return []
    except Exception as ex:
        logger.error(f"查询热门动态时发生意外错误 (mid={mid}): {ex}")
        return []


async def query_current_videos(mid):
    """
    从数据库获取指定用户最新发布的视频信息。
    Query the most recent video information for a given user from the database.

    Args:
        mid: 用户 UID。

    Returns:
        List: 包含最新视频信息的列表，格式同 query_all_video_list_by_mid 返回的单行。
              如果查询失败或无数据，则返回包含 None 的列表。
    """
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT bvid, video_name, description, cover, datetime, play_num, comment_num, like_num, coin, favorite_num, share_num, danmuku_num, aid, length, honor_short, honor_count, honor,video_ai_conclusion
                FROM videos_table
                WHERE uid = $1
                ORDER BY datetime DESC
                LIMIT 1
            """
            latest_video = await conn.fetchrow(sql_query, mid)

            if latest_video:
                logger.info(
                    f"从数据库获取到最新的视频 (mid={mid}): {latest_video['bvid']}"
                )
                return [
                    latest_video["bvid"],
                    latest_video["video_name"],
                    latest_video["description"],
                    latest_video["cover"],
                    (
                        latest_video["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                        if latest_video["datetime"]
                        else None
                    ),
                    latest_video["play_num"],
                    latest_video["comment_num"],
                    latest_video["like_num"],
                    latest_video["coin"],
                    latest_video["favorite_num"],
                    latest_video["share_num"],
                    latest_video["danmuku_num"],
                    latest_video["aid"],
                    latest_video["length"],
                    latest_video["honor_short"],
                    latest_video["honor_count"],
                    latest_video["honor"],
                    latest_video["video_ai_conclusion"],
                ]
            else:
                logger.warning(f"数据库中未找到 mid={mid} 的任何视频")
                return [None] * 18  # Adjusted count to match fields

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询最新视频时出错 (query_current_videos, mid={mid}): {e}")
        return [None] * 18  # Adjusted count
    except Exception as ex:
        logger.error(f"查询最新视频时发生意外错误 (mid={mid}): {ex}")
        return [None] * 18  # Adjusted count


async def query_current_dynamics(mid):
    """
    从数据库获取指定用户最新发布的动态信息。
    Query the most recent dynamic information for a given user from the database.

    Args:
        mid: 用户 UID。

    Returns:
        List: 包含最新动态信息的列表，格式同 query_user_dynamics_by_mid 返回的单行。
              如果查询失败或无数据，则返回包含 None 的列表。
    """
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT name, datetime, dynamic_content, url, topic, dynamic_id, share_num, comment_num, like_num, comment_id, comment_type
                FROM dynamics_table
                WHERE uid = $1
                ORDER BY datetime DESC
                LIMIT 1
            """
            latest_dynamic = await conn.fetchrow(sql_query, mid)

            if latest_dynamic:
                logger.info(
                    f"从数据库获取到最新的动态 (mid={mid}): {latest_dynamic['dynamic_id']}"
                )
                return [
                    latest_dynamic["name"],
                    (
                        latest_dynamic["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                        if latest_dynamic["datetime"]
                        else None
                    ),
                    latest_dynamic["dynamic_content"],
                    latest_dynamic["url"],
                    latest_dynamic["topic"],
                    latest_dynamic["dynamic_id"],
                    latest_dynamic["share_num"],
                    latest_dynamic["comment_num"],
                    latest_dynamic["like_num"],
                    latest_dynamic["comment_id"],
                    latest_dynamic["comment_type"],
                ]
            else:
                logger.warning(f"数据库中未找到 mid={mid} 的任何动态")
                return [None] * 11

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询最新动态时出错 (query_current_dynamics, mid={mid}): {e}")
        return [None] * 11
    except Exception as ex:
        logger.error(f"查询最新动态时发生意外错误 (mid={mid}): {ex}")
        return [None] * 11


async def query_recent_top_n_videos(mid, rencent, n):
    """
    从数据库获取指定用户最近发布的 n 个视频信息。
    Query the most recent n videos for a given user from the database.

    Args:
        mid: 用户 UID。
        rencent: 最近 n 个视频。

    Returns:
        List: 包含最近 n 个视频信息的列表，格式同 query_all_video_list_by_mid 返回的单行。
    """
    start_time = datetime.now() - timedelta(days=rencent)
    end_time = datetime.now()
    res = await query_top_n_videos(
        mid, start_time.strftime("%Y-%m-%d"), end_time.strftime("%Y-%m-%d"), n
    )
    return res


async def query_comments_for_wordcloud(mid, start_time_str, end_time_str):
    """
    从数据库获取指定用户在指定时间范围内的所有视频和动态评论文本。
    Query all video and dynamic comment texts for a given user within a time range for word cloud generation.

    Args:
        mid: 用户 UID。
        start_time_str: 开始时间字符串 (YYYY-MM-DD)。
        end_time_str: 结束时间字符串 (YYYY-MM-DD)。

    Returns:
        List[str]: 包含所有评论文本的列表。
                   如果查询失败或无数据，则返回空列表。
    """
    try:
        start_time_dt = datetime.strptime(start_time_str, "%Y-%m-%d")
        end_time_dt = datetime.strptime(end_time_str, "%Y-%m-%d") + timedelta(days=1)
    except ValueError:
        logger.error("无效的日期格式，请使用 'YYYY-MM-DD'")
        return []

    all_comments_text = []
    video_comment_table_name = fatal_sql.get_video_comment_table_name(mid)
    dynamics_comment_table_name = fatal_sql.get_dynamics_comment_table_name(mid)

    try:
        async with get_connection() as conn:
            if video_comment_table_name:
                video_comment_sql = f"""
                    SELECT vc.comment
                    FROM "{video_comment_table_name}" vc
                    JOIN videos_table v ON vc.bvid = v.bvid
                    WHERE v.uid = $1 AND vc.datetime >= $2 AND vc.datetime < $3
                """
                video_comments = await conn.fetch(
                    video_comment_sql, mid, start_time_dt, end_time_dt
                )
                if video_comments:
                    all_comments_text.extend(
                        [row["comment"] for row in video_comments if row["comment"]]
                    )
                    logger.info(
                        f"获取到 {len(video_comments)} 条视频评论用于词云 (mid={mid})"
                    )

            if dynamics_comment_table_name:
                # Assuming 'time' column in dynamics_comment_table is equivalent to 'datetime'
                dynamic_comment_sql = f"""
                    SELECT dc.comment
                    FROM "{dynamics_comment_table_name}" dc
                    JOIN dynamics_table d ON dc.oid = d.dynamic_id 
                    WHERE d.uid = $1 AND dc.datetime >= $2 AND dc.datetime < $3
                """  # Corrected join condition and time column
                dynamic_comments = await conn.fetch(
                    dynamic_comment_sql, mid, start_time_dt, end_time_dt
                )
                if dynamic_comments:
                    all_comments_text.extend(
                        [row["comment"] for row in dynamic_comments if row["comment"]]
                    )
                    logger.info(
                        f"获取到 {len(dynamic_comments)} 条动态评论用于词云 (mid={mid})"
                    )

        if not all_comments_text:
            logger.warning(f"在指定时间范围内未找到 mid={mid} 的任何评论用于词云")

        return all_comments_text

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询评论文本时出错 (query_comments_for_wordcloud, mid={mid}): {e}"
        )
        return []
    except Exception as ex:
        logger.error(f"查询评论文本时发生意外错误 (mid={mid}): {ex}")
        return []


async def query_tieba_whole(mid, start_time_str=None, end_time_str=None):
    """
    从数据库获取指定贴吧在指定时间范围内的完整数据（主题帖、帖子、评论）。
    Query complete Tieba data (threads, posts, comments) for a given Tieba name within a time range from the database.

    Args:
        mid: 主播UID (对应 tieba_whole_table_{mid} 中的 up_uid).
        start_time_str: 开始时间字符串 (YYYY-MM-DD)，可选。
        end_time_str: 结束时间字符串 (YYYY-MM-DD)，可选。

    Returns:
        List[List]: 包含贴吧数据的列表的列表，格式类似原 CSV。
                    如果查询失败或无数据，则返回空列表。
    """
    table_name = fatal_sql.get_tieba_whole_table_name(mid)
    if not table_name:
        logger.error(f"无法确定 mid={mid} 的贴吧表名")
        return []

    params = [mid]
    time_conditions_list = []

    try:
        if start_time_str:
            start_time_dt = datetime.strptime(start_time_str, "%Y-%m-%d").date()
            time_conditions_list.append(f"create_time::date >= ${len(params) + 1}")
            params.append(start_time_dt)
        if end_time_str:
            end_time_dt = (
                datetime.strptime(end_time_str, "%Y-%m-%d") + timedelta(days=1)
            ).date()
            time_conditions_list.append(f"create_time::date < ${len(params) + 1}")
            params.append(end_time_dt)
    except ValueError:
        logger.error("无效的日期格式，请使用 'YYYY-MM-DD'")
        return []

    time_condition_str = (
        (" AND " + " AND ".join(time_conditions_list)) if time_conditions_list else ""
    )

    try:
        async with get_connection() as conn:
            # Note: Table name is dynamically inserted. Ensure it's safe.
            sql_query_str = f"""
                SELECT fid, fname, tid, user_name, create_time, last_time, title, text, img, view_num, reply_num, agree, disagree, level_num, pid, floor
                FROM "{table_name}"
                WHERE up_uid = $1{time_condition_str}
                ORDER BY tid DESC, floor ASC, create_time ASC
            """
            results = await conn.fetch(sql_query_str, *params)

            if results:
                formatted_data = [
                    [
                        row["fid"],
                        row["fname"],
                        row["tid"],
                        row["user_name"],
                        (
                            row["create_time"].strftime("%Y-%m-%d %H:%M:%S")
                            if row["create_time"]
                            else None
                        ),
                        (
                            row["last_time"].strftime("%Y-%m-%d %H:%M:%S")
                            if row["last_time"]
                            else None
                        ),
                        row["title"],
                        row["text"],
                        row["img"],
                        row["view_num"],
                        row["reply_num"],
                        row["agree"],
                        row["disagree"],
                        row["level_num"],
                        row["pid"],
                        row["floor"],
                    ]
                    for row in results
                ]
                logger.info(
                    f"从数据库获取到 {len(formatted_data)} 条 {mid} 的完整贴吧数据"
                )
                return formatted_data
            else:
                logger.info(f"数据库中无 {mid} 在指定时间范围的完整贴吧数据")
                return []

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询贴吧完整数据时出错 (query_tieba_whole, mid={mid}): {e}")
        return []
    except Exception as ex:
        logger.error(f"查询贴吧完整数据时发生意外错误 (mid={mid}): {ex}")
        return []


async def query_tieba_threads(mid, start_time_str=None, end_time_str=None):
    """
    从数据库获取指定贴吧在指定时间范围内的的主题帖列表。
    Query Tieba thread list for a given Tieba name within a time range from the database.

    Args:
        mid: 主播 UID (对应 tieba_threads_table 中的 up_uid).
        start_time_str: 开始时间字符串 (YYYY-MM-DD)，可选。
        end_time_str: 结束时间字符串 (YYYY-MM-DD)，可选。

    Returns:
        List[List]: 包含主题帖数据的列表的列表，格式类似原 CSV。
                    如果查询失败或无数据，则返回空列表。
    """
    params = [mid]
    time_conditions_list = []
    try:
        if start_time_str:
            start_time_dt = datetime.strptime(start_time_str, "%Y-%m-%d").date()
            time_conditions_list.append(f"create_time::date >= ${len(params) + 1}")
            params.append(start_time_dt)
        if end_time_str:
            end_time_dt = (
                datetime.strptime(end_time_str, "%Y-%m-%d") + timedelta(days=1)
            ).date()
            time_conditions_list.append(f"create_time::date < ${len(params) + 1}")
            params.append(end_time_dt)
    except ValueError:
        logger.error("无效的日期格式，请使用 'YYYY-MM-DD'")
        return []

    time_condition_str = (
        (" AND " + " AND ".join(time_conditions_list)) if time_conditions_list else ""
    )

    try:
        async with get_connection() as conn:
            sql_query_str = f"""
                SELECT up_uid, fid, fname, tid, user_name, create_time, last_time, title, text, img, view_num, reply_num, share_num, agree, disagree
                FROM tieba_threads_table
                WHERE up_uid = $1{time_condition_str}
                ORDER BY create_time DESC
            """
            results = await conn.fetch(sql_query_str, *params)

            if results:
                formatted_data = [
                    [
                        row["fid"],
                        row["fname"],
                        row["tid"],
                        row["user_name"],
                        (
                            row["create_time"].strftime("%Y-%m-%d %H:%M:%S")
                            if row["create_time"]
                            else None
                        ),
                        (
                            row["last_time"].strftime("%Y-%m-%d %H:%M:%S")
                            if row["last_time"]
                            else None
                        ),
                        row["title"],
                        row["text"],
                        row["img"],
                        row["view_num"],
                        row["reply_num"],
                        row["share_num"],
                        row["agree"],
                        row["disagree"],
                    ]
                    for row in results
                ]
                logger.info(f"从数据库获取到 {len(formatted_data)} 条 {mid} 的主题帖")
                return formatted_data
            else:
                logger.info(f"数据库中无 {mid} 在指定时间范围的主题帖")
                return []

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询贴吧主题帖时出错 (query_tieba_threads, mid={mid}): {e}")
        return []
    except Exception as ex:
        logger.error(f"查询贴吧主题帖时发生意外错误 (mid={mid}): {ex}")
        return []


async def query_all_video_comments_by_mid(mid):
    """
    从数据库查询指定用户所有视频的所有评论。
    Query all comments for all videos of a given user from the database.

    Args:
        mid: 用户 UID (主播的 UID)。

    Returns:
        List[List]: 包含评论信息的列表的列表，格式类似原 CSV：
                    ['up_name', '昵称', '评论', '等级'(None), '时间', '点赞数', 'mid', 'rpid', 'face', '本楼层数', '父评论rpid', 'bvid', 'sentiment']
                    如果查询失败或无数据，则返回空列表。
    """
    video_comment_table_name = fatal_sql.get_video_comment_table_name(mid)
    if not video_comment_table_name:
        logger.warning(f"无法确定 mid={mid} 的视频评论表名")
        return []
    try:
        async with get_connection() as conn:
            # Use $1, $2 for placeholders. Table name is f-stringed.
            query_str = f"""
                SELECT vc.up_name, vc.uname, vc.comment, vc.datetime, vc.like_num, vc.mid, vc.rpid, vc.face, vc.rcount, vc.parent_rpid, vc.oid, vc.heat, vc.sentiment
                FROM "{video_comment_table_name}" vc 
                WHERE vc.up_uid = $1
                ORDER BY vc.datetime DESC
            """
            results = await conn.fetch(query_str, mid)

            if results:
                formatted_data = [
                    [
                        row["up_name"],
                        row["uname"],
                        row["comment"],
                        (
                            row["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                            if row["datetime"]
                            else None
                        ),
                        row["like_num"],
                        row["mid"],
                        row["rpid"],
                        row["face"],
                        row["rcount"],
                        row["parent_rpid"],
                        row["oid"],
                        row["heat"],
                        row["sentiment"],
                    ]
                    for row in results
                ]
                logger.info(
                    f"从数据库获取到 {len(formatted_data)} 条 mid={mid} 的视频评论"
                )
                return formatted_data
            else:
                logger.info(f"数据库中无 mid={mid} 的视频评论")
                return []

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询所有视频评论时出错 (query_all_video_comments_by_mid, mid={mid}): {e}"
        )
        return []
    except Exception as ex:
        logger.error(f"查询所有视频评论时发生意外错误 (mid={mid}): {ex}")
        return []


async def query_all_dynamics_comments_by_mid(mid):
    """
    从数据库查询指定用户所有动态的所有评论。
    Query all comments for all dynamics of a given user from the database.

    Args:
        mid: 用户 UID (主播的 UID)。

    Returns:
        List[List]: 包含评论信息的列表的列表，格式类似原 CSV：
                    如果查询失败或无数据，则返回空列表。
    """
    dynamics_comment_table_name = fatal_sql.get_dynamics_comment_table_name(mid)
    if not dynamics_comment_table_name:
        logger.warning(f"无法确定 mid={mid} 的动态评论表名")
        return []
    try:
        async with get_connection() as conn:
            query_str = f"""
                SELECT dc.up_name, dc.uname, dc.comment, dc.datetime, dc.like_num, dc.mid, dc.rpid, dc.face, dc.rcount, dc.parent_rpid, dc.oid, dc.heat, dc.sentiment
                FROM "{dynamics_comment_table_name}" dc
                WHERE dc.up_uid = $1
                ORDER BY dc.datetime DESC
            """
            results = await conn.fetch(query_str, mid)

            if results:
                formatted_data = [
                    [
                        row["up_name"],
                        row["uname"],
                        row["comment"],
                        (
                            row["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                            if row["datetime"]
                            else None
                        ),
                        row["like_num"],
                        row["mid"],
                        row["rpid"],
                        row["face"],
                        row["rcount"],
                        row["parent_rpid"],
                        row["oid"],
                        row["heat"],
                        row["sentiment"],
                    ]
                    for row in results
                ]
                logger.info(
                    f"从数据库获取到 {len(formatted_data)} 条 mid={mid} 的动态评论"
                )
                return formatted_data
            else:
                logger.info(f"数据库中无 mid={mid} 的动态评论")
                return []

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询所有动态评论时出错 (query_all_dynamics_comments_by_mid, mid={mid}): {e}"
        )
        return []
    except Exception as ex:
        logger.error(f"查询所有动态评论时发生意外错误 (mid={mid}): {ex}")
        return []


async def query_recent_video_day_views(mid, recent_days: int):
    """
    从数据库查询指定用户最近几天的所有视频每日观看数据。
    Query daily video view data for all videos of a given user for recent days.

    Args:
        mid: 用户 UID。
        recent_days: 最近的天数。

    Returns:
        List[List]: 包含每日观看数据的列表的列表。
                    每个子列表格式: [bvid, date_str, view_num]
                    如果查询失败或无数据，则返回空列表。
    """
    try:
        start_time_dt = datetime.now() - timedelta(days=recent_days)
    except ValueError:
        logger.error("无效的 recent_days 值")
        return []

    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT DISTINCT ON (bvid, datetime::date)
                       bvid, datetime::date as view_date, view_num
                FROM video_day_data_table
                WHERE uid = $1 AND datetime >= $2
                ORDER BY bvid, datetime::date, timestamp DESC
            """
            results = await conn.fetch(sql_query, mid, start_time_dt)

            if results:
                formatted_data = [
                    [
                        row["bvid"],
                        row["view_date"].strftime("%Y-%m-%d"),
                        row["view_num"],
                    ]
                    for row in results
                ]
                formatted_data.sort(key=lambda x: x[1])
                logger.info(
                    f"获取到 {len(formatted_data)} 条 mid={mid} 最近 {recent_days} 天的视频日观看数据"
                )
                return formatted_data
            else:
                logger.info(
                    f"数据库中无 mid={mid} 最近 {recent_days} 天的视频日观看数据"
                )
                return []

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询最近视频日观看数据时出错 (query_recent_video_day_views, mid={mid}): {e}"
        )
        return []
    except Exception as ex:
        logger.error(f"查询最近视频日观看数据时发生意外错误 (mid={mid}): {ex}")
        return []


async def query_current_video_day_views(bvid):
    """
    从数据库查询指定视频的最新一条日观看数据。
    Query the most recent daily view data for a specific video of a given user.

    Args:
        bvid: 视频 BVID。

    Returns:
        List: 包含最新观看数据的列表: [bvid, date_str, view_num]
              如果查询失败或无数据，则返回包含 None 的列表。
    """
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT bvid, datetime::date as view_date, view_num
                FROM video_day_data_table
                WHERE bvid = $1
                ORDER BY datetime DESC
                LIMIT 1
            """
            result = await conn.fetchrow(sql_query, bvid)

            if result:
                logger.info(f"获取到视频 {bvid}  的最新日观看数据")
                return [
                    result["bvid"],
                    result["view_date"].strftime("%Y-%m-%d"),
                    result["view_num"],
                ]
            else:
                logger.info(f"数据库中无视频 {bvid}的日观看数据")
                return [bvid, None, None]

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询当前视频日观看数据时出错 (query_current_video_day_views, bvid={bvid}): {e}"
        )
        return [bvid, None, None]
    except Exception as ex:
        logger.error(f"查询当前视频日观看数据时发生意外错误 bvid={bvid}): {ex}")
        return [bvid, None, None]


async def query_target_video_day_views(mid, bvid: str, target_date_str: str):
    """
    从数据库查询指定视频在特定日期的观看数据。
    Query the daily view data for a specific video of a given user on a target date.

    Args:
        mid: 用户 UID。
        bvid: 视频 BVID。
        target_date_str: 目标日期字符串 (YYYY-MM-DD)。

    Returns:
        List: 包含目标日期观看数据的列表: [bvid, date_str, view_num]
              如果查询失败、日期无效或无数据，则返回包含 None 的列表。
    """
    try:
        target_date = datetime.strptime(target_date_str, "%Y-%m-%d").date()
    except ValueError:
        logger.error("无效的目标日期格式，请使用 'YYYY-MM-DD'")
        return [bvid, target_date_str, None]

    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT bvid, datetime::date as view_date, view_num
                FROM video_day_data_table
                WHERE uid = $1 AND bvid = $2 AND datetime::date = $3
                LIMIT 1
            """
            result = await conn.fetchrow(sql_query, mid, bvid, target_date)

            if result:
                logger.info(
                    f"获取到视频 {bvid} (mid={mid}) 在 {target_date_str} 的日观看数据"
                )
                return [
                    result["bvid"],
                    result["view_date"].strftime("%Y-%m-%d"),
                    result["view_num"],
                ]
            else:
                logger.info(
                    f"数据库中无视频 {bvid} (mid={mid}) 在 {target_date_str} 的日观看数据"
                )
                return [bvid, target_date_str, None]

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询目标视频日观看数据时出错 (query_target_video_day_views, mid={mid}, bvid={bvid}, date={target_date_str}): {e}"
        )
        return [bvid, target_date_str, None]
    except Exception as ex:
        logger.error(
            f"查询目标视频日观看数据时发生意外错误 (mid={mid}, bvid={bvid}, date={target_date_str}): {ex}"
        )
        return [bvid, target_date_str, None]


async def query_recent_relationships(mid, target_date: str, recent_days: int):
    """
    从数据库查询指定用户在特定日期生成的最近几天的关系数据。
    Query recent relationship data generated on a specific date for a given user from the database.

    Args:
        mid: 用户 UID。
        target_date: 目标生成日期 (str, YYYY-MM-DD).
        recent_days: 生成关系数据时使用的天数。

    Returns:
        str: 包含关系数据的 JSON 字符串。如果查询失败、日期无效或无数据，则返回 None。
    """
    try:
        target_date_only = datetime.strptime(target_date, "%Y-%m-%d").date()
    except ValueError:
        logger.error(f"无效的目标日期格式: {target_date}")
        return None

    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT relationships
                FROM ai_gen_table
                WHERE uid = $1
                  AND cur_datetime::date <= $2
                  AND recent = $3
                  AND relationships IS NOT NULL
                ORDER BY cur_timestamp DESC
                LIMIT 1
            """
            result = await conn.fetchval(sql_query, mid, target_date_only, recent_days)

            if result:
                logger.info(
                    f"从数据库获取到 mid={mid} 在 {target_date_only} 生成的最近 {recent_days} 天的关系数据"
                )
                return result
            else:
                logger.info(
                    f"数据库中无 mid={mid} 在 {target_date_only} 生成的最近 {recent_days} 天的关系数据"
                )
                return None

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询近期关系数据时出错 (query_recent_relationships, mid={mid}, date={target_date_only}, recent={recent_days}): {e}"
        )
        return None
    except Exception as ex:
        logger.error(
            f"查询近期关系数据时发生意外错误 (mid={mid}, date={target_date_only}, recent={recent_days}): {ex}"
        )
        return None


async def query_tieba_summaries_from_ai_gen_table(uid: str, recent: int):
    """
    从 ai_gen_table 查询指定 uid 和 recent 的 tieba_summaries。
    Query tieba_summaries from ai_gen_table for a given uid and recent days.

    Args:
        uid: 用户 UID。
        recent: 最近的天数 (对应 ai_gen_table 中的 recent 字段)。

    Returns:
        str or None: 包含 tieba_summaries 的 JSON 字符串，如果查询失败或无数据则返回 None。
    """
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT tieba_summaries
                FROM ai_gen_table
                WHERE uid = $1
                  AND recent = $2
                  AND tieba_summaries IS NOT NULL
                ORDER BY cur_timestamp DESC
                LIMIT 1
            """
            result = await conn.fetchval(sql_query, uid, recent)

            if result:
                logger.info(
                    f"从数据库获取到 UID={uid} 最近 {recent} 天的 tieba_summaries"
                )
                return result
            else:
                logger.info(
                    f"数据库中无 UID={uid} 最近 {recent} 天的 tieba_summaries 或内容为空"
                )
                return None

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询 tieba_summaries 时出错 (uid={uid}, recent={recent}): {e}")
        return None
    except Exception as ex:
        logger.error(
            f"查询 tieba_summaries 时发生意外错误 (uid={uid}, recent={recent}): {ex}"
        )
        return None


async def query_tieba_summaries_from_ai_gen_table_by_date(
    uid: str, target_date: str, recent: int
):
    """
    从 ai_gen_table 查询指定 uid, target_date 和 recent 的 tieba_summaries。
    Query tieba_summaries from ai_gen_table for a given uid, target_date and recent days.

    Args:
        uid: 用户 UID。
        target_date: 目标生成日期 (str, YYYY-MM-DD).
        recent: 最近的天数 (对应 ai_gen_table 中的 recent 字段)。

    Returns:
        str or None: 包含 tieba_summaries 的 JSON 字符串，如果查询失败或无数据则返回 None。
    """
    try:
        target_date_only = datetime.strptime(target_date, "%Y-%m-%d").date()
    except ValueError:
        logger.error(f"无效的目标日期格式: {target_date}")
        return None

    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT tieba_summaries
                FROM ai_gen_table
                WHERE uid = $1
                  AND cur_datetime::date <= $2
                  AND recent = $3
                  AND tieba_summaries IS NOT NULL
                ORDER BY cur_timestamp DESC
                LIMIT 1
            """
            result = await conn.fetchval(sql_query, uid, target_date_only, recent)
            result = json.loads(result) if result else None

            if result:
                logger.info(
                    f"从数据库获取到 UID={uid} 在 {target_date_only} 生成的最近 {recent} 天的 tieba_summaries"
                )
                return result
            else:
                logger.info(
                    f"数据库中无 UID={uid} 在 {target_date_only} 生成的最近 {recent} 天的 tieba_summaries 或内容为空"
                )
                return None

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询 tieba_summaries (by date) 时出错 (uid={uid}, date={target_date_only}, recent={recent}): {e}"
        )
        return None
    except Exception as ex:
        logger.error(
            f"查询 tieba_summaries (by date) 时发生意外错误 (uid={uid}, date={target_date_only}, recent={recent}): {ex}"
        )
        return None


async def query_rise_reason_from_ai_gen_table(uid: str, recent: int = 30):
    """
    从 ai_gen_table 查询指定 uid 和 recent 的 rise_reason。
    Query rise_reason from ai_gen_table for a given uid and recent days.

    Args:
        uid: 用户 UID。
        recent: 最近的天数 (对应 ai_gen_table 中的 recent 字段)。

    Returns:
        str or None: 包含 rise_reason 的 JSON 字符串，如果查询失败或无数据则返回 None。
    """
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT rise_reason
                FROM ai_gen_table
                WHERE uid = $1
                  AND recent = $2
                  AND rise_reason IS NOT NULL
                ORDER BY cur_timestamp DESC
                LIMIT 1
            """
            result = await conn.fetchval(sql_query, uid, recent)

            if result:
                logger.info(f"从数据库获取到 UID={uid} 最近 {recent} 天的 rise_reason")
                return result
            else:
                logger.info(
                    f"数据库中无 UID={uid} 最近 {recent} 天的 rise_reason 或内容为空"
                )
                return None

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询 rise_reason 时出错 (uid={uid}, recent={recent}): {e}")
        return None
    except Exception as ex:
        logger.error(
            f"查询 rise_reason 时发生意外错误 (uid={uid}, recent={recent}): {ex}"
        )
        return None


async def query_rise_reason_from_ai_gen_table_by_date(
    uid: str, target_date: str, recent: int
):
    """
    从 ai_gen_table 查询指定 uid, target_date 和 recent 的 rise_reason。
    Query rise_reason from ai_gen_table for a given uid, target_date and recent days.

    Args:
        uid: 用户 UID。
        target_date: 目标生成日期 (str, YYYY-MM-DD).
        recent: 最近的天数 (对应 ai_gen_table 中的 recent 字段)。

    Returns:
        str or None: 包含 rise_reason 的 JSON 字符串，如果查询失败或无数据则返回 None。
    """
    try:
        target_date_only = datetime.strptime(
            target_date, "%Y-%m-%d"
        ).date()  # Corrected .str to .date()
    except ValueError:
        logger.error(f"无效的目标日期格式: {target_date}")
        return None

    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT rise_reason
                FROM ai_gen_table
                WHERE uid = $1
                  AND cur_datetime::date = $2
                  AND recent = $3
                  AND rise_reason IS NOT NULL
                ORDER BY cur_timestamp DESC
                LIMIT 1
            """
            result = await conn.fetchval(sql_query, uid, target_date_only, recent)

            if result:
                logger.info(
                    f"从数据库获取到 UID={uid} 在 {target_date_only} 生成的最近 {recent} 天的 rise_reason"
                )
                return result
            else:
                logger.info(
                    f"数据库中无 UID={uid} 在 {target_date_only} 生成的最近 {recent} 天的 rise_reason 或内容为空"
                )
                return None

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询 rise_reason (by date) 时出错 (uid={uid}, date={target_date_only}, recent={recent}): {e}"
        )
        return None
    except Exception as ex:
        logger.error(
            f"查询 rise_reason (by date) 时发生意外错误 (uid={uid}, date={target_date_only}, recent={recent}): {ex}"
        )
        return None


async def query_latest_fans_medal_rank(uid: str):
    """
    查询指定 UID 最新的粉丝牌排行榜数据(前十)。
    Query the latest fans medal rank data for a given UID.

    Args:
        uid: 用户 UID。

    Returns:
        asyncpg.Record or None: 包含最新粉丝牌排行榜数据的记录，或 None 如果查询失败或无数据。
    """
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT uid, name, liveid, datetime, rank_list
                FROM fans_medal_rank_table
                WHERE uid = $1
                ORDER BY datetime DESC
                LIMIT 1
            """
            result = await conn.fetchrow(sql_query, uid)
            if result:
                result = dict(result)
                result["rank_list"] = (
                    json.loads(result["rank_list"]) if result["rank_list"] else []
                )

            if result:
                logger.info(f"从数据库获取到 UID={uid} 的最新粉丝牌排行榜数据")
                return result
            else:
                logger.info(f"数据库中无 UID={uid} 的粉丝牌排行榜数据")
                return None

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询最新粉丝牌排行榜数据时出错 (query_latest_fans_medal_rank, uid={uid}): {e}"
        )
        return None
    except Exception as ex:
        logger.error(f"查询最新粉丝牌排行榜数据时发生意外错误 (uid={uid}): {ex}")
        return None


async def query_fans_medal_rank_by_datetime(uid: str, target_datetime_str: str):
    """
    查询指定 UID 在指定 datetime 的粉丝牌排行榜数据(前十)。
    Query fans medal rank data for a given UID at a specific datetime.

    Args:
        uid: 用户 UID。
        target_datetime_str: 目标 datetime (str, YYYY-MM-DD).

    Returns:
        asyncpg.Record or None: 包含指定 datetime 粉丝牌排行榜数据的记录，或 None 如果查询失败或无数据。
    """
    try:
        target_date = datetime.strptime(target_datetime_str, "%Y-%m-%d").date()
    except ValueError:
        logger.error(f"无效的目标日期时间格式: {target_datetime_str}")
        return None

    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT uid, name, liveid, datetime, rank_list
                FROM fans_medal_rank_table
                WHERE uid = $1 AND datetime::date = $2
                LIMIT 1
            """
            result = await conn.fetchrow(sql_query, uid, target_date)

            if result:
                logger.info(
                    f"从数据库获取到 UID={uid} 在 {target_date} 的粉丝牌排行榜数据"
                )
                return result
            else:
                logger.info(f"数据库中无 UID={uid} 在 {target_date} 的粉丝牌排行榜数据")
                return None

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询指定 datetime 粉丝牌排行榜数据时出错 (query_fans_medal_rank_by_datetime, uid={uid}, datetime={target_date}): {e}"
        )
        return None
    except Exception as ex:
        logger.error(
            f"查询指定 datetime 粉丝牌排行榜数据时发生意外错误 (uid={uid}, datetime={target_date}): {ex}"
        )
        return None


async def query_video_ai_conclusion_by_bvid(bvid: str):
    """
    查询指定 bvid 视频的 AI 总结。
    Query the AI conclusion for a specific video BVID from the videos_table.

    Args:
        bvid: 视频 BVID。

    Returns:
        str or None: 视频的 AI 总结文本，如果查询失败或无数据则返回 None。
    """
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT video_ai_conclusion
                FROM videos_table
                WHERE bvid = $1
                LIMIT 1
            """
            result = await conn.fetchval(sql_query, bvid)

            if result:  # fetchval returns the value directly or None
                logger.info(f"成功查询到 BVID={bvid} 的 video_ai_conclusion")
                return result
            else:  # Handles both no row and NULL value cases
                logger.info(
                    f"数据库中未找到 BVID={bvid} 的记录或 video_ai_conclusion 为空"
                )
                return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询 BVID={bvid} 的 video_ai_conclusion 时出错: {e}")
        return None
    except Exception as ex:
        logger.error(f"查询 BVID={bvid} 的 video_ai_conclusion 时发生意外错误: {ex}")
        return None


async def query_single_video_day_data(bvid: str, target_date_str: str):
    """
    查询指定视频在指定天的播放量/日增。
    Query the view count and daily increase for a specific video on a specific day,
    returning the complete row from the database.

    Args:
        bvid: 视频 BVID。
        target_date_str: 目标日期字符串 (YYYY-MM-DD)。

    Returns:
        asyncpg.Record or None: 包含指定视频和日期的数据的记录，
                                         如果查询失败、日期无效或无数据则返回 None。
    """
    try:
        target_date = datetime.strptime(target_date_str, "%Y-%m-%d").date()
    except ValueError:
        logger.error(f"无效的目标日期格式: {target_date_str}，请使用 'YYYY-MM-DD'")
        return None

    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT *
                FROM video_day_data_table
                WHERE bvid = $1 AND datetime::date = $2
                ORDER BY datetime DESC
                LIMIT 1
            """
            result = await conn.fetchrow(sql_query, bvid, target_date)

            if result:
                logger.info(
                    f"成功查询到BVID={bvid}, Date={target_date_str} 的视频日数据"
                )
                return result
            else:
                logger.info(
                    f"数据库中未找到 BVID={bvid}, Date={target_date_str} 的视频日数据"
                )
                return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询单条视频日数据时出错 (BVID={bvid}, Date={target_date_str}): {e}"
        )
        return None
    except Exception as ex:
        logger.error(
            f"查询单条视频日数据时发生意外错误 (BVID={bvid}, Date={target_date_str}): {ex}"
        )
        return None


async def query_single_video_rencent_day_data(bvid: str, recent: int):
    """
    查询指定视频在最近几天的播放量/日增。
    Query the view count and daily increase for a specific video on the recent days,
    returning the complete row from the database.

    Args:
        bvid: 视频 BVID。
        recent: 最近几天，例如 7 表示最近 7 天。

    Returns:
        List[asyncpg.Record] or None: 包含指定视频和日期的数据的记录列表，
                                         如果查询失败、日期无效或无数据则返回 None。
    """
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT *
                FROM video_day_data_table
                WHERE bvid = $1
                ORDER BY datetime DESC
                LIMIT $2
            """
            results = await conn.fetch(sql_query, bvid, recent)

            if results:
                logger.info(f"成功查询到BVID={bvid}, 最近 {recent} 天的视频日数据")
                return results
            else:
                logger.info(f"数据库中未找到 BVID={bvid}, 最近 {recent} 天的视频日数据")
                return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询单条视频日数据时出错 (BVID={bvid}, 最近 {recent} 天): {e}")
        return None
    except Exception as ex:
        logger.error(
            f"查询单条视频日数据时发生意外错误 (BVID={bvid}, 最近 {recent} 天): {ex}"
        )
        return None


async def query_single_video_target_day_data(bvid: str, target_date_str: str):
    """
    查询指定视频在指定天的播放量/日增。
    Query the view count and daily increase for a specific video on a specific day,
    returning the complete row from the database.

    Args:
        bvid: 视频 BVID。
        target_date_str: 目标日期字符串 (YYYY-MM-DD)。

    Returns:
        asyncpg.Record or None: 包含指定视频和日期的数据的记录，
                                         如果查询失败、日期无效或无数据则返回 None。
    """
    try:
        target_date = datetime.strptime(target_date_str, "%Y-%m-%d").date()
    except ValueError:
        logger.error(f"无效的目标日期格式: {target_date_str}，请使用 'YYYY-MM-DD'")
        return None

    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT *
                FROM video_day_data_table
                WHERE bvid = $1 AND datetime::date = $2
                ORDER BY datetime DESC
                LIMIT 1
            """
            result = await conn.fetchrow(sql_query, bvid, target_date)

            if result:
                logger.info(
                    f"成功查询到BVID={bvid}, Date={target_date_str} 的视频日数据"
                )
                return result
            else:
                logger.info(
                    f"数据库中未找到 BVID={bvid}, Date={target_date_str} 的视频日数据"
                )
                return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询单条视频日数据时出错 (BVID={bvid}, Date={target_date_str}): {e}"
        )
        return None
    except Exception as ex:
        logger.error(
            f"查询单条视频日数据时发生意外错误 (BVID={bvid}, Date={target_date_str}): {ex}"
        )
        return None


async def query_top_n_view_rise_recent_day_data(uid, n: int):
    """
    查询最近一天播放量日增排名前 n 的视频。
    Query the top n videos with the highest view count increase on the recent day.

    Args:
        uid: 用户 UID.
        n: 查询的条数。

    Returns:
        List[asyncpg.Record] or None: 包含最近一天播放量日增排名前 n 的视频的列表，
                                               如果查询失败、日期无效或无数据则返回 None。
    """
    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT *
                FROM video_day_data_table
                WHERE uid = $1
                ORDER BY datetime DESC, view_rise_num DESC NULLS LAST 
                LIMIT $2 
            """  # Added view_rise_num to ORDER BY
            results = await conn.fetch(sql_query, uid, n)

            if results:
                logger.info(
                    f"成功查询到最近一天播放量日增排名前 {n} 的视频 (uid={uid})"
                )
                return results
            else:
                logger.info(
                    f"数据库中未找到最近一天播放量日增排名前 {n} 的视频 (uid={uid})"
                )
                return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询最近一天播放量日增排名前 {n} 的视频时出错 (uid={uid}): {e}")
        return None
    except Exception as ex:
        logger.error(
            f"查询最近一天播放量日增排名前 {n} 的视频时发生意外错误 (uid={uid}): {ex}"
        )
        return None


async def query_top_n_view_rise_target_day_data(uid, target_date_str: str, n: int):
    """
    查询指定日期播放量日增排名前 n 的视频。
    Query the top n videos with the highest view count increase on a specific day.

    Args:
        uid: 用户 UID.
        target_date_str: 目标日期字符串 (YYYY-MM-DD)。
        n: 查询的条数。

    Returns:
        List[asyncpg.Record] or None: 包含指定日期播放量日增排名前 n 的视频的列表，
                                               如果查询失败、日期无效或无数据则返回 None。
    """
    try:
        target_date = datetime.strptime(target_date_str, "%Y-%m-%d").date()
    except ValueError:
        logger.error(f"无效的目标日期格式: {target_date_str}，请使用 'YYYY-MM-DD'")
        return None

    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT *
                FROM video_day_data_table
                WHERE uid = $1 AND datetime::date = $2
                ORDER BY view_rise_num DESC NULLS LAST
                LIMIT $3
            """
            results = await conn.fetch(sql_query, uid, target_date, n)

            if results:
                logger.info(
                    f"成功查询到 Date={target_date_str} 播放量日增排名前 {n} 的视频 (uid={uid})"
                )
                return results
            else:
                logger.info(
                    f"数据库中未找到 Date={target_date_str} 播放量日增排名前 {n} 的视频 (uid={uid})"
                )
                return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询播放量日增排名前 {n} 的视频时出错 (uid={uid}, Date={target_date_str}): {e}"
        )
        return None
    except Exception as ex:
        logger.error(
            f"查询播放量日增排名前 {n} 的视频时发生意外错误 (uid={uid}, Date={target_date_str}): {ex}"
        )
        return None


async def query_top_n_view_rise_day_data_period(
    uid, start_time: str, end_time: str, n: int
):
    """
    查询指定时间区间播放量日增排名前 n 的视频。
    Query the top n videos with the highest view count increase on a specific day.

    Args:
        uid: 用户 UID.
        start_time: 起始日期字符串 (YYYY-MM-DD)。
        end_time: 结束日期字符串 (YYYY-MM-DD)。
        n: 查询的条数。

    Returns:
        List[asyncpg.Record] or None: 包含指定日期播放量日增排名前 n 的视频的列表，
                                               如果查询失败、日期无效或无数据则返回 None。
    """
    try:
        start_date = datetime.strptime(start_time, "%Y-%m-%d").date()
        end_date = datetime.strptime(end_time, "%Y-%m-%d").date()
    except ValueError:
        logger.error(
            f"无效的目标日期格式: {start_time} 或 {end_time}，请使用 'YYYY-MM-DD'"
        )
        return None

    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT *
                FROM video_day_data_table
                WHERE uid = $1 AND datetime::date >= $2 AND datetime::date <= $3
                ORDER BY view_rise_num DESC NULLS LAST
                LIMIT $4
            """
            results = await conn.fetch(sql_query, uid, start_date, end_date, n)

            if results:
                logger.info(
                    f"成功查询到 Date={start_time} 至 {end_time} 播放量日增排名前 {n} 的视频 (uid={uid})"
                )
                return results
            else:
                logger.info(
                    f"数据库中未找到 Date={start_time} 至 {end_time} 播放量日增排名前 {n} 的视频 (uid={uid})"
                )
                return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询播放量日增排名前 {n} 的视频时出错 (uid={uid}, Date={start_time} 至 {end_time}): {e}"
        )
        return None
    except Exception as ex:
        logger.error(
            f"查询播放量日增排名前 {n} 的视频时发生意外错误 (uid={uid}, Date={start_time} 至 {end_time}): {ex}"
        )
        return None


async def query_current_follower_change_num(uid, recent_days: int = 1):
    """
    查询指定用户的当前粉丝变化量。
    Query the current follower change number of a specified user.

    Args:
        uid: 用户 UID。

    Returns:
        int or None: 包含指定用户的当前粉丝变化量的整数，如果查询失败或无数据则返回 None。
    """
    try:
        async with get_connection() as conn:
            today_date = datetime.now().date()
            yesterday_date = today_date - timedelta(days=recent_days)

            sql_today = """
                SELECT follower_num
                FROM current_stat_table
                WHERE uid = $1 AND datetime::date = $2
                ORDER BY datetime DESC
                LIMIT 1
            """
            today_follower_num = await conn.fetchval(sql_today, uid, today_date)

            sql_yesterday = """
                SELECT follower_num
                FROM current_stat_table
                WHERE uid = $1 AND datetime::date <= $2
                ORDER BY datetime DESC
                LIMIT 1
            """
            yesterday_follower_num = await conn.fetchval(
                sql_yesterday, uid, yesterday_date
            )

            if today_follower_num is not None and yesterday_follower_num is not None:
                change = today_follower_num - yesterday_follower_num
                logger.info(
                    f"成功计算 UID={uid} 的当前粉丝变化量: {change} (今日: {today_follower_num}, 昨日: {yesterday_follower_num})"
                )
                return change
            elif today_follower_num is not None:
                logger.warning(
                    f"UID={uid} 只有今天的粉丝数据 ({today_follower_num})，无法计算变化量"
                )
                return today_follower_num
            else:
                logger.warning(f"UID={uid} 未找到今日或昨日的粉丝数据，无法计算变化量")
                return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询 UID={uid} 的当前粉丝变化量时出错: {e}")
        return None
    except Exception as ex:
        logger.error(f"查询 UID={uid} 的当前粉丝变化量时发生意外错误: {ex}")
        return None


async def query_current_dahanghai_change_num(uid, recent_days: int = 1):
    """
    查询指定用户的当前大航海变化量。
    Query the current dahanghai change number of a specified user.

    Args:
        uid: 用户 UID。

    Returns:
        int or None: 包含指定用户的当前大航海变化量的整数，如果查询失败或无数据则返回 None。
    """
    try:
        async with get_connection() as conn:
            today_date = datetime.now().date()
            yesterday_date = today_date - timedelta(days=recent_days)

            sql_today = """
                SELECT dahanghai_num
                FROM current_stat_table
                WHERE uid = $1 AND datetime::date = $2
                ORDER BY datetime DESC
                LIMIT 1
            """
            today_dahanghai_num = await conn.fetchval(sql_today, uid, today_date)

            sql_yesterday = """
                SELECT dahanghai_num
                FROM current_stat_table
                WHERE uid = $1 AND datetime::date <= $2
                ORDER BY datetime DESC
                LIMIT 1
            """
            yesterday_dahanghai_num = await conn.fetchval(
                sql_yesterday, uid, yesterday_date
            )

            if today_dahanghai_num is not None and yesterday_dahanghai_num is not None:
                change = today_dahanghai_num - yesterday_dahanghai_num
                logger.info(
                    f"成功计算 UID={uid} 的当前大航海变化量: {change} (今日: {today_dahanghai_num}, 昨日: {yesterday_dahanghai_num})"
                )
                return change
            elif today_dahanghai_num is not None:
                logger.warning(
                    f"UID={uid} 只有今天的大航海数据 ({today_dahanghai_num})，无法计算变化量"
                )
                return today_dahanghai_num
            else:
                logger.warning(
                    f"UID={uid} 未找到今日或昨日的大航海数据，无法计算变化量"
                )
                return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询 UID={uid} 的当前大航海变化量时出错: {e}")
        return None
    except Exception as ex:
        logger.error(f"查询 UID={uid} 的当前大航海变化量时发生意外错误: {ex}")
        return None


async def query_latest_dahanghai_list_by_uid(up_uid: str):
    """
    查询指定 up_uid 最新的大航海列表。
    Query the latest Dahanghai list for a given up_uid.

    Args:
        up_uid: 主播的 UID。

    Returns:
        List[asyncpg.Record] or None: 包含最新大航海列表数据的记录列表，
                             如果查询失败或无数据则返回 None。
    """
    try:
        async with get_connection() as conn:
            latest_datetime_sql = """
                SELECT MAX(datetime) as max_dt
                FROM dahanghai_list_table
                WHERE up_uid = $1;
            """
            max_datetime = await conn.fetchval(latest_datetime_sql, up_uid)

            if not max_datetime:
                logger.info(f"数据库中未找到 up_uid={up_uid} 的大航海数据")
                return None

            sql_query = """
                SELECT up_uid, up_name, time, datetime, num, page, uid, ruid, rank, username, face, guard_level, guard_sub_level, if_top3
                FROM dahanghai_list_table
                WHERE up_uid = $1 AND datetime = $2
                ORDER BY rank ASC;
            """
            results = await conn.fetch(sql_query, up_uid, max_datetime)

            if results:
                logger.info(
                    f"成功查询到 up_uid={up_uid} 最新的大航海列表数据 (datetime: {max_datetime})"
                )
                return results  # Returns list of asyncpg.Record
            else:
                logger.warning(
                    f"数据库中找到 up_uid={up_uid} 的最新 datetime ({max_datetime}) 但未找到对应的大航海列表数据"
                )
                return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询 up_uid={up_uid} 最新的大航海列表时出错: {e}")
        return None
    except Exception as ex:
        logger.error(f"查询 up_uid={up_uid} 最新的大航海列表时发生意外错误: {ex}")
        return None


async def query_dahanghai_list_by_uid_and_datetime(
    up_uid: str, target_datetime_str: str
):
    """
    查询指定 up_uid 在指定 datetime 的大航海列表。
    Query the Dahanghai list for a given up_uid and a specific datetime.

    Args:
        up_uid: 主播的 UID。
        target_datetime_str: 目标日期时间字符串 (例如'YYYY-MM-DD').

    Returns:
        List[asyncpg.Record] or None: 包含指定 datetime 大航海列表数据的记录列表，
                             如果查询失败或无数据则返回 None。
    """
    try:
        target_date = datetime.strptime(target_datetime_str, "%Y-%m-%d").date()
    except ValueError:
        logger.error(f"无效的目标日期时间格式: {target_datetime_str}")
        return None

    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT up_uid, up_name, time, datetime, num, page, uid, ruid, rank, username, face, guard_level, guard_sub_level, if_top3
                FROM dahanghai_list_table
                WHERE up_uid = $1 AND datetime::date = $2
                ORDER BY rank ASC;
            """
            results = await conn.fetch(sql_query, up_uid, target_date)

            if results:
                logger.info(
                    f"成功查询到 up_uid={up_uid} 在 {target_datetime_str} 的大航海列表数据"
                )
                return results
            else:
                logger.info(
                    f"数据库中未找到 up_uid={up_uid} 在 {target_datetime_str} 的大航海列表数据"
                )
                return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询 up_uid={up_uid} 在 {target_datetime_str} 的大航海列表时出错: {e}"
        )
        return None
    except Exception as ex:
        logger.error(
            f"查询 up_uid={up_uid} 在 {target_datetime_str} 的大航海列表时发生意外错误: {ex}"
        )
        return None


async def query_recent_info(uid, room_id, rencent: int = 30) -> VupRencentInfo:
    """
    查询指定用户的综合数据：
    VupRencentInfo(
            time,
            name, # query_user_info_by_mid -> [1]
            follower_change, # query_current_follower_change_num
            dahanghai_change, # query_current_dahanghai_change_num
            video_content, # query_current_videos -> [datetime, video_name, play_num, honor_short]
            dynamic_content, # query_current_dynamics -> [datetime, dynamic_content]
            live_content, # query_user_info_by_mid -> [8] (live_title)
            relations, # query_recent_relationships(uid, cur_time, 30)
            rise_videos, # query_top_n_view_rise_recent_day_data(uid, cur_time, 3)
            tieba_topic, # query_tieba_summaries_from_ai_gen_table(uid, 30)
        )

    Args:
        uid: 用户 UID。

    Returns:
        VupRencentInfo or None: 包含指定用户的综合数据的对象，如果查询失败或无数据则返回 None。
    """
    try:
        current_time = datetime.now()
        time_str = current_time.strftime("%Y-%m-%d")
        user_info = await query_user_info_by_mid(uid)
        name = (
            user_info["name"] if user_info and "name" in user_info else "未知用户"
        )  # Access by key
        live_info = await query_now_live_info_by_room(room_id)
        live_title = (
            live_info[5] if live_info and len(live_info) > 5 else "暂无直播信息"
        )  # Check length

        follower_change = await query_current_follower_change_num(uid)
        dahanghai_change = await query_current_dahanghai_change_num(uid)

        current_videos_raw = await query_current_videos(uid)
        video_content_list = []
        if (
            current_videos_raw
            and all(v is not None for v in current_videos_raw)
            and len(current_videos_raw) > 14
        ):  # Check length
            video_dt_str = current_videos_raw[4]
            video_name = current_videos_raw[1]
            play_num = current_videos_raw[5]
            honor_short = current_videos_raw[14]
            video_content_list = [
                str(video_dt_str),
                str(video_name),
                str(play_num),
                str(honor_short),
            ]
        else:
            video_content_list = ["暂无", "暂无视频", "0", ""]

        current_dynamics_raw = await query_current_dynamics(uid)
        dynamic_content_list = []
        if (
            current_dynamics_raw
            and any(d is not None for d in current_dynamics_raw)
            and len(current_dynamics_raw) > 2
        ):  # Check length
            dynamic_dt_str = current_dynamics_raw[1]
            dynamic_text = current_dynamics_raw[2]
            dynamic_content_list = [str(dynamic_dt_str), str(dynamic_text)]
        else:
            dynamic_content_list = ["暂无", "暂无动态"]

        relations_json_str = await query_recent_relationships(uid, time_str, rencent)
        relations_data = None
        if relations_json_str:
            try:
                relations_data = json.loads(relations_json_str)
            except json.JSONDecodeError:
                logger.error(f"解析 UID={uid} 的关系数据时出错: {relations_json_str}")
                relations_data = {"error": "关系数据解析失败"}
        else:
            relations_data = {"info": "暂无关系数据"}

        rise_videos_raw = await query_top_n_view_rise_recent_day_data(uid, 3)
        rise_videos_list = []
        if rise_videos_raw:
            for video_row in rise_videos_raw:  # video_row is asyncpg.Record
                rise_videos_list.append(
                    [
                        video_row.get("bvid", "N/A"),
                        video_row.get(
                            "video_name", "N/A"
                        ),  # Assuming 'video_name' is the correct key
                        video_row.get(
                            "title", "N/A"
                        ),  # Keep if needed, or remove if 'video_name' is sufficient
                        video_row.get("view_num", 0),
                        video_row.get("view_rise_num", 0),
                    ]
                )
        else:
            rise_videos_list = [
                ["N/A", "暂无飙升视频", "N/A", 0, 0]
            ]  # Adjusted default

        tieba_topic_json_str = await query_tieba_summaries_from_ai_gen_table(
            uid, rencent
        )
        tieba_topic_data = None
        if tieba_topic_json_str:
            try:
                tieba_topic_data = json.loads(tieba_topic_json_str)
            except json.JSONDecodeError:
                logger.error(f"解析 UID={uid} 的贴吧主题时出错: {tieba_topic_json_str}")
                tieba_topic_data = {"error": "贴吧主题解析失败"}
        else:
            tieba_topic_data = {"info": "暂无贴吧主题"}

        return VupRencentInfo(
            time=time_str,
            name=name,
            follower_change=follower_change if follower_change is not None else 0,
            dahanghai_change=dahanghai_change if dahanghai_change is not None else 0,
            video_content=video_content_list,
            dynamic_content=dynamic_content_list,
            live_content=live_title,
            relations=relations_data,
            rise_videos=rise_videos_list,
            tieba_topic=tieba_topic_data,
        )

    except Exception as e:
        logger.error(f"查询用户 {uid} 近期综合信息时发生错误: {e}")
        return VupRencentInfo(
            time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            name=f"用户{uid}",
            follower_change=0,
            dahanghai_change=0,
            video_content=["错误", "获取视频信息失败", "0", ""],
            dynamic_content=["错误", "获取动态信息失败"],
            live_content="获取直播信息失败",
            relations={"error": "获取关系数据失败"},
            rise_videos=[["N/A", "获取飙升视频失败", "N/A", 0, 0]],  # Adjusted default
            tieba_topic={"error": "获取贴吧主题失败"},
        )


async def query_recent_info_with_view(uid) -> VupRencentInfo:
    """
    查询指定用户的综合数据（通过视图 vup_recent_info_view）：
    VupRencentInfo(
            time,
            name,
            follower_change,
            dahanghai_change,
            video_content, # [datetime, video_name, play_num, honor_short]
            dynamic_content, # [datetime, dynamic_content]
            live_content, # live_title
            relations,
            rise_videos, # 飙升视频仍需单独查询
            tieba_topic, # 贴吧主题从视图获取
        )

    Args:
        uid: 用户 UID。

    Returns:
        VupRencentInfo or None: 包含指定用户的综合数据的对象，如果查询失败或无数据则返回 None。
    """
    try:
        current_time = datetime.now()
        time_str = current_time.strftime("%Y-%m-%d %H:%M:%S")
        view_data = None
        async with get_connection() as conn:
            sql_query_view = "SELECT * FROM vup_recent_info_view WHERE uid = $1"
            view_data = await conn.fetchrow(sql_query_view, uid)

        if view_data:
            name = view_data.get("user_name", "未知用户")
            live_title = view_data.get("live_title", "暂无直播信息")

            video_dt = view_data.get("video_timestamp")
            video_content_list = [
                video_dt.strftime("%Y-%m-%d %H:%M:%S") if video_dt else "暂无",
                view_data.get("video_name", "暂无视频"),
                str(view_data.get("video_play_num", 0)),
                view_data.get("video_honor_short", ""),
            ]

            dynamic_dt = view_data.get("dynamic_timestamp")
            dynamic_content_list = [
                dynamic_dt.strftime("%Y-%m-%d %H:%M:%S") if dynamic_dt else "暂无",
                view_data.get("dynamic_content", "暂无动态"),
            ]

            relations_json_str = view_data.get("ai_relationships_summary")
            relations_data = (
                json.loads(relations_json_str)
                if relations_json_str
                else {"info": "暂无关系数据"}
            )

            tieba_topic_json_str = view_data.get("ai_tieba_summary")
            tieba_topic_data = (
                json.loads(tieba_topic_json_str)
                if tieba_topic_json_str
                else {"info": "暂无贴吧主题"}
            )
        else:
            logger.warning(f"未能从视图 vup_recent_info_view 获取到 UID={uid} 的数据。")
            # Fallback or default values if view_data is None
            user_info_fallback = await query_user_info_by_mid(uid)
            name = (
                user_info_fallback["name"]
                if user_info_fallback
                else f"用户{uid} (视图查询失败)"
            )
            live_info_fallback = await query_now_live_info_by_room(uid)
            live_title = (
                live_info_fallback[5]
                if live_info_fallback and len(live_info_fallback) > 5
                else "视图获取失败"
            )

            current_videos_fallback = await query_current_videos(uid)
            video_content_list = (
                [
                    current_videos_fallback[4].strftime("%Y-%m-%d %H:%M:%S")
                    if current_videos_fallback and current_videos_fallback[4]
                    else "错误",
                    current_videos_fallback[1]
                    if current_videos_fallback and current_videos_fallback[1]
                    else "视图获取失败",
                    str(current_videos_fallback[5])
                    if current_videos_fallback
                    and current_videos_fallback[5] is not None
                    else "0",
                    current_videos_fallback[14]
                    if current_videos_fallback and current_videos_fallback[14]
                    else "",
                ]
                if current_videos_fallback
                else ["错误", "视图获取失败", "0", ""]
            )

            current_dynamics_fallback = await query_current_dynamics(uid)
            dynamic_content_list = (
                [
                    current_dynamics_fallback[1].strftime("%Y-%m-%d %H:%M:%S")
                    if current_dynamics_fallback and current_dynamics_fallback[1]
                    else "错误",
                    current_dynamics_fallback[2]
                    if current_dynamics_fallback and current_dynamics_fallback[2]
                    else "视图获取失败",
                ]
                if current_dynamics_fallback
                else ["错误", "视图获取失败"]
            )

            relations_fallback_str = await query_recent_relationships(
                uid, time_str, 30
            )  # Assuming 30 days recent
            relations_data = (
                json.loads(relations_fallback_str)
                if relations_fallback_str
                else {"error": "视图获取失败"}
            )

            tieba_topic_fallback_str = await query_tieba_summaries_from_ai_gen_table(
                uid, 30
            )  # Assuming 30 days recent
            tieba_topic_data = (
                json.loads(tieba_topic_fallback_str)
                if tieba_topic_fallback_str
                else {"error": "视图获取失败"}
            )

        follower_change = await query_current_follower_change_num(uid)
        dahanghai_change = await query_current_dahanghai_change_num(uid)

        rise_videos_raw = await query_top_n_view_rise_target_day_data(
            uid, current_time.strftime("%Y-%m-%d"), 3
        )
        rise_videos_list = []
        if rise_videos_raw:
            for video_row in rise_videos_raw:
                video_title = video_row.get("video_name", "N/A")  # Use 'video_name'
                rise_videos_list.append(
                    [
                        video_row.get("bvid", "N/A"),
                        video_title,
                        video_row.get("view_rise_num", 0),
                    ]
                )
        else:
            rise_videos_list = (
                [["N/A", "暂无飙升视频", 0]]
                if view_data
                else [["N/A", "视图获取失败", 0]]
            )

        return VupRencentInfo(
            time=time_str,
            name=name,
            follower_change=(follower_change if follower_change is not None else 0),
            dahanghai_change=(dahanghai_change if dahanghai_change is not None else 0),
            video_content=video_content_list,
            dynamic_content=dynamic_content_list,
            live_content=live_title,
            relations=relations_data,
            rise_videos=rise_videos_list,
            tieba_topic=tieba_topic_data,
        )

    except json.JSONDecodeError as je:
        logger.error(f"JSON解析错误 (UID={uid}): {je}")  # Specific error for JSON
        # Fallback for JSON error
        return VupRencentInfo(
            time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            name=f"用户{uid} (JSON解析异常)",
            follower_change=0,
            dahanghai_change=0,
            video_content=["错误", "JSON解析失败", "0", ""],
            dynamic_content=["错误", "JSON解析失败"],
            live_content="JSON解析失败",
            relations={"error": "JSON解析失败"},
            rise_videos=[["N/A", "JSON解析失败", 0]],
            tieba_topic={"error": "JSON解析失败"},
        )
    except Exception as e:
        logger.error(f"查询用户 {uid} 近期综合信息时发生错误 (with_view): {e}")
        return VupRencentInfo(
            time=datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            name=f"用户{uid} (查询异常)",
            follower_change=0,
            dahanghai_change=0,
            video_content=["错误", "获取视频信息失败", "0", ""],
            dynamic_content=["错误", "获取动态信息失败"],
            live_content="获取直播信息失败",
            relations={"error": "获取关系数据失败"},
            rise_videos=[["N/A", "获取飙升视频失败", 0]],
            tieba_topic={"error": "获取贴吧主题失败"},
        )


async def query_followers_list(
    up_uid: str, target_date_str: str, recent_days: int = 7
):  # Renamed target_date to target_date_str
    """
    查询指定 up_uid 在最近 recent_days 天内新增的关注者列表。
    Query the followers list added within the recent_days for a given up_uid.

    Args:
        up_uid: 被关注者的 UID。
        target_date_str: 目标查询日期字符串 (YYYY-MM-DD).
        recent_days: 查询最近多少天内的数据。

    Returns:
        List[asyncpg.Record] or None: 包含关注者信息的记录列表，
                                               如果查询失败或无数据则返回 None。
    """
    try:
        target_date_obj = datetime.strptime(
            target_date_str, "%Y-%m-%d"
        )  # Use target_date_str
        start_time_dt = target_date_obj - timedelta(days=recent_days)
        start_timestamp = int(start_time_dt.timestamp())

        async with get_connection() as conn:
            sql_query = """
                SELECT follower_uid, follower_name, face_url, sign, follow_time, record_timestamp
                FROM followers_list_table
                WHERE up_uid = $1 AND follow_time >= $2
                ORDER BY follow_time DESC
            """
            results = await conn.fetch(sql_query, up_uid, start_timestamp)

            if results:
                logger.info(
                    f"成功查询到 up_uid={up_uid} 最近 {recent_days} 天内的 {len(results)} 条新增关注者数据"
                )
                return results
            else:
                logger.info(
                    f"数据库中未找到 up_uid={up_uid} 最近 {recent_days} 天内的新增关注者数据"
                )
                return None

    except ValueError:
        logger.error(f"无效的日期格式: {target_date_str}")
        return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询 up_uid={up_uid} 最近 {recent_days} 天关注者列表时出错: {e}")
        return None
    except Exception as ex:
        logger.error(
            f"查询 up_uid={up_uid} 最近 {recent_days} 天关注者列表时发生意外错误: {ex}"
        )
        return None


async def query_followers_review_list(
    up_uid: str, target_date_str: str, recent_days: int = 7
):
    """
    从数据库的 follower_review_table 表中获取指定up_uid在target_date_str（及其前recent_days天）的关注者审查数据。
    Args:
        up_uid: 被关注者的 UID。
        target_date_str: 目标查询日期字符串 (YYYY-MM-DD)。数据将从 (target_date - recent_days + 1) 到 target_date。
        recent_days: 查询最近多少天内的数据。

    Returns:
        List[asyncpg.Record] or None: 包含关注者审查信息的记录列表，
                                               如果查询失败或无数据则返回 None。
    """
    try:
        target_date_obj = datetime.strptime(target_date_str, "%Y-%m-%d").date()
        start_date_obj = target_date_obj - timedelta(days=recent_days)

        async with get_connection() as conn:
            table_name = "follower_review_table"  # Table name is static here
            sql_query = f"""
                SELECT *
                FROM "{table_name}"
                WHERE up_uid = $1 AND query_date >= $2 AND query_date <= $3 AND if_review IS NOT NULL
                ORDER BY query_date DESC
            """
            results = await conn.fetch(
                sql_query, up_uid, start_date_obj, target_date_obj
            )

            if results:
                logger.info(
                    f"成功查询到 up_uid={up_uid} 从 {start_date_obj} 到 {target_date_obj} 的 {len(results)} 条关注者审查数据"
                )
                return results
            else:
                logger.info(
                    f"数据库中未找到 up_uid={up_uid} 从 {start_date_obj} 到 {target_date_obj} 的关注者审查数据"
                )
                return []

    except ValueError:
        logger.error(f"无效的日期格式: {target_date_str}，请使用 'YYYY-MM-DD'")
        return None
    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(f"查询 up_uid={up_uid} 关注者审查列表时数据库出错: {e}")
        return None
    except Exception as ex:
        logger.error(f"查询 up_uid={up_uid} 关注者审查列表时发生意外错误: {ex}")
        return None


async def query_followers_review_rate(uid: str, target_date_str: str, recent: int = 7):
    """
    从数据库的 follower_review_table 表中获取数据，计算指定 uid 和日期的 review_rate。

    Args:
        uid: 用户 UID (对应表中的 up_uid)。
        target_date_str: 目标日期字符串 (YYYY-MM-DD)。
        recent: 对应表中的 recent_days 字段，用于筛选。

    Returns:
        str: 格式化的 review_rate 字符串 (e.g., "10.5%") 或 "0%" 或 "Error"。
    """
    try:
        end_date_obj = datetime.strptime(target_date_str, "%Y-%m-%d").date()
        start_date_obj = end_date_obj - timedelta(days=recent)
    except ValueError:
        logger.error(f"无效的日期格式: {target_date_str}，请使用 'YYYY-MM-DD'")
        return "Error"

    try:
        async with get_connection() as conn:
            sql_query = """
                SELECT if_review
                FROM follower_review_table
                WHERE up_uid = $1 AND query_date >= $2 AND query_date <= $3 AND if_review IS NOT NULL
            """
            results = await conn.fetch(sql_query, uid, start_date_obj, end_date_obj)

            if not results:
                logger.info(
                    f"在 {start_date_obj} 到 {end_date_obj} 范围内未找到 UID={uid} 的关注者审查数据"
                )
                return "0%"

            total_count = len(results)
            reviewed_count = sum(1 for row in results if row["if_review"] is True)

            if total_count == 0:
                return "0%"

            rate = (reviewed_count / total_count) * 100
            return f"{rate:.1f}%"

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询 UID={uid} 在 {start_date_obj} 到 {end_date_obj} 的关注者审查率时数据库出错: {e}"
        )
        return "Error"
    except Exception as ex:
        logger.error(
            f"查询 UID={uid} 在 {start_date_obj} 到 {end_date_obj} 的关注者审查率时发生意外错误: {ex}"
        )
        return "Error"


async def query_recent_comments_sentiment_value(
    up_uid: str,
    target_time_str: str,
    recent_days: int = 7,
    source: Choice = Choice(["all", "bili", "tieba"]),
):
    """
    查询指定up主在目标日期前N天内，来自不同来源的评论/帖子的情感值。

    Args:
        up_uid: UP主的UID。
        target_time_str: 目标日期字符串，格式为 'YYYY-MM-DD'。查询将以此日期为结束点，向前追溯recent_days。
        recent_days: 从目标日期向前追溯的天数。
        source: 数据来源，可以是 'all', 'bili', 'tieba'。

    Returns:
        Dict: 包含情感比例和评论信息的字典。
              如果查询失败或无数据，则返回包含默认值的字典。
    """
    all_comments_data = []
    try:
        target_datetime_obj = datetime.strptime(target_time_str, "%Y-%m-%d")
        end_time_dt = target_datetime_obj.replace(
            hour=23, minute=59, second=59, microsecond=999999
        )
        start_time_dt = end_time_dt - timedelta(days=recent_days)
    except ValueError:
        logger.error(f"无效的日期格式: {target_time_str}，请使用 'YYYY-MM-DD'")
        return {
            "love_ratio": 0,
            "positive_ratio": 0,
            "neutral_ratio": 0,
            "critical_ratio": 0,
            "negative_ratio": 0,
            "info": [],
        }

    actual_source = source.value if isinstance(source, Choice) else source

    try:
        async with get_connection() as conn:
            if actual_source in ["all", "bili"]:
                video_comment_table_name = fatal_sql.get_video_comment_table_name(
                    up_uid
                )
                if video_comment_table_name:
                    try:
                        video_comment_sql = f"""
                            SELECT datetime, comment, sentiment
                            FROM "{video_comment_table_name}"
                            WHERE up_uid = $1 AND datetime >= $2 AND datetime <= $3 AND sentiment IS NOT NULL
                        """
                        video_comments = await conn.fetch(
                            video_comment_sql, up_uid, start_time_dt, end_time_dt
                        )
                        if video_comments:
                            all_comments_data.extend(
                                [dict(row) for row in video_comments]
                            )
                            logger.info(
                                f"从 {video_comment_table_name} 获取到 {len(video_comments)} 条视频评论情感数据"
                            )
                    except asyncpg.exceptions.UndefinedTableError:
                        logger.warning(
                            f"视频评论表 {video_comment_table_name} 不存在，跳过。"
                        )
                    except Exception as e_vc:
                        logger.error(
                            f"查询视频评论表 {video_comment_table_name} 时出错: {e_vc}"
                        )

                dynamics_comment_table_name = fatal_sql.get_dynamics_comment_table_name(
                    up_uid
                )
                if dynamics_comment_table_name:
                    try:
                        dynamic_comment_sql = f"""
                            SELECT datetime, comment, sentiment
                            FROM "{dynamics_comment_table_name}"
                            WHERE up_uid = $1 AND datetime >= $2 AND datetime <= $3 AND sentiment IS NOT NULL
                        """
                        dynamic_comments = await conn.fetch(
                            dynamic_comment_sql, up_uid, start_time_dt, end_time_dt
                        )
                        if dynamic_comments:
                            all_comments_data.extend(
                                [dict(row) for row in dynamic_comments]
                            )
                            logger.info(
                                f"从 {dynamics_comment_table_name} 获取到 {len(dynamic_comments)} 条动态评论情感数据"
                            )
                    except asyncpg.exceptions.UndefinedTableError:
                        logger.warning(
                            f"动态评论表 {dynamics_comment_table_name} 不存在，跳过。"
                        )
                    except Exception as e_dc:
                        logger.error(
                            f"查询动态评论表 {dynamics_comment_table_name} 时出错: {e_dc}"
                        )

            if actual_source in ["all", "tieba"]:
                tieba_whole_table_name = fatal_sql.get_tieba_whole_table_name(
                    up_uid
                )  # This should be the actual table name

                # Get tieba name from vups.json using up_uid
                from utils.utils import read_vups_config
                vups = read_vups_config()
                tieba_fname = None
                for vup in vups:
                    if str(vup["uid"]) == str(up_uid):
                        tieba_fname = vup["tiebaName"]
                        break

                if (
                    tieba_whole_table_name and tieba_fname
                ):  # Check if both table name and fname are found
                    try:
                        tieba_sql = f"""
                            SELECT create_time as datetime, text, sentiment
                            FROM "{tieba_whole_table_name}"
                            WHERE fname = $1 AND create_time >= $2 AND create_time <= $3 AND sentiment IS NOT NULL AND text IS NOT NULL AND text != ''
                        """
                        tieba_posts = await conn.fetch(
                            tieba_sql, tieba_fname, start_time_dt, end_time_dt
                        )  # Use tieba_fname
                        if tieba_posts:
                            all_comments_data.extend([dict(row) for row in tieba_posts])
                            logger.info(
                                f"从 {tieba_whole_table_name} (fname: {tieba_fname}) 获取到 {len(tieba_posts)} 条贴吧帖子情感数据"
                            )
                    except asyncpg.exceptions.UndefinedTableError:
                        logger.warning(
                            f"贴吧表 {tieba_whole_table_name} 不存在，跳过。"
                        )
                    except Exception as e_tb:
                        logger.error(
                            f"查询贴吧表 {tieba_whole_table_name} (fname: {tieba_fname}) 时出错: {e_tb}"
                        )
                else:
                    logger.warning(
                        f"未能获取到 up_uid={up_uid} 的贴吧表名或fname，跳过贴吧数据查询。 Table: {tieba_whole_table_name}, Fname: {tieba_fname}"
                    )

        if not all_comments_data:
            logger.warning(
                f"在指定时间范围和来源 ({actual_source}) 内未找到 up_uid={up_uid} 的任何带情感值的评论/帖子"
            )
            return {
                "love_ratio": 0,
                "positive_ratio": 0,
                "neutral_ratio": 0,
                "critical_ratio": 0,
                "negative_ratio": 0,
                "info": [],
            }

        all_comments_data.sort(
            key=lambda x: x["datetime"] if x.get("datetime") else datetime.min,
            reverse=True,
        )  # Handle None datetime

        total_count = len(all_comments_data)
        if (
            total_count == 0
        ):  # Should be caught by the previous check, but good for safety
            return {
                "love_ratio": 0,
                "positive_ratio": 0,
                "neutral_ratio": 0,
                "critical_ratio": 0,
                "negative_ratio": 0,
                "info": [],
            }

        love_ratio = round(
            sum(1 for item in all_comments_data if item.get("sentiment", 0) > 0.8)
            / total_count,
            4,
        )
        positive_ratio = round(
            sum(
                1 for item in all_comments_data if 0.5 < item.get("sentiment", 0) <= 0.8
            )
            / total_count,
            4,
        )
        neutral_ratio = round(
            sum(
                1 for item in all_comments_data if 0.3 < item.get("sentiment", 0) <= 0.5
            )
            / total_count,
            4,
        )
        critical_ratio = round(
            sum(
                1 for item in all_comments_data if 0.1 < item.get("sentiment", 0) <= 0.3
            )
            / total_count,
            4,
        )
        negative_ratio = round(
            1 - love_ratio - positive_ratio - neutral_ratio - critical_ratio, 4
        )

        # Format datetime for output and ensure text is present
        formatted_info = [
            {
                "datetime": item["datetime"].strftime("%Y-%m-%d %H:%M:%S")
                if item.get("datetime")
                else None,
                "text": item.get("comment")
                or item.get("text", ""),  # Use 'comment' or 'text'
                "sentiment": item.get("sentiment"),
            }
            for item in all_comments_data
        ]

        merged_data_agg = defaultdict(lambda: {"comments": [], "sentiments": []})
        for item in formatted_info:
            if item["datetime"]:  # Only process if datetime is not None
                merged_data_agg[item["datetime"]]["comments"].append(item["text"])
                merged_data_agg[item["datetime"]]["sentiments"].append(
                    item["sentiment"]
                )

        new_info_agg = []
        for time_key, values_agg in merged_data_agg.items():
            avg_sentiment = (
                round(mean(values_agg["sentiments"]), 4)
                if values_agg["sentiments"]
                else 0
            )
            new_info_agg.append(
                {
                    "time": time_key,
                    "comment": values_agg["comments"],  # List of comments at this time
                    "sentiment": avg_sentiment,
                }
            )
        new_info_agg.sort(key=lambda x: x["time"], reverse=True)

        res = {
            "love_ratio": love_ratio,
            "positive_ratio": positive_ratio,
            "neutral_ratio": neutral_ratio,
            "critical_ratio": critical_ratio,
            "negative_ratio": negative_ratio,
            "info": new_info_agg,  # Use aggregated and sorted info
        }
        return res

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"查询评论情感值时数据库出错 (up_uid={up_uid}, source={actual_source}): {e}"
        )
        return {
            "love_ratio": 0,
            "positive_ratio": 0,
            "neutral_ratio": 0,
            "critical_ratio": 0,
            "negative_ratio": 0,
            "info": [],
        }
    except Exception as ex:
        logger.error(
            f"查询评论情感值时发生意外错误 (up_uid={up_uid}, source={actual_source}): {ex}"
        )
        return {
            "love_ratio": 0,
            "positive_ratio": 0,
            "neutral_ratio": 0,
            "critical_ratio": 0,
            "negative_ratio": 0,
            "info": [],
        }


async def query_comment_wordcloud(mid, char_zh, start_time_str: str, end_time_str: str):
    """
    从数据库获取指定用户在指定时间范围内的视频评论、动态评论和相关贴吧内容，生成词云图。

    OPTIMIZED VERSION: Includes intelligent file-based caching and robust error handling.

    Args:
        mid: 用户 UID
        char_zh: 用户中文名称
        start_time_str: 开始时间字符串 (YYYY-MM-DD)
        end_time_str: 结束时间字符串 (YYYY-MM-DD)

    Returns:
        tuple: (word_cloud_path, is_cached) or None if generation failed
               - word_cloud_path: Relative URL path to the image
               - is_cached: Boolean indicating if the result was served from cache
    """
    # Create cache key based on parameters
    cache_key = hashlib.md5(f"wordcloud_{mid}_{char_zh}_{start_time_str}_{end_time_str}".encode()).hexdigest()

    # Create deterministic filename for file-based caching
    cache_filename = f"{char_zh}_{cache_key[:12]}.png"
    cache_file_path = f"{PROJECT_ROOT}/frontend/public/wordcloud/{cache_filename}"
    relative_path = f"/wordcloud/{cache_filename}"

    # Check file-based cache first
    current_time = datetime.now()
    if os.path.exists(cache_file_path):
        # Check if file is still valid (within TTL)
        file_stat = os.stat(cache_file_path)
        file_age = current_time.timestamp() - file_stat.st_mtime

        if file_age < _wordcloud_cache_ttl_seconds:
            logger.info(f"返回缓存的词云图片文件 (mid={mid}, file={cache_filename}, age={file_age:.0f}s)")
            return (relative_path, True)
        else:
            # File is expired, remove it
            try:
                os.remove(cache_file_path)
                logger.info(f"删除过期的词云缓存文件 (file={cache_filename}, age={file_age:.0f}s)")
            except OSError:
                pass

    try:
        start_time_dt = datetime.strptime(start_time_str, "%Y-%m-%d")
        end_time_dt_query_upper_bound = datetime.strptime(
            end_time_str, "%Y-%m-%d"
        ) + timedelta(days=1)
    except ValueError:
        logger.error(
            f"无效的日期格式，请使用 'YYYY-MM-DD'. start_time='{start_time_str}', end_time='{end_time_str}'"
        )
        return None

    all_texts = []

    try:
        async with get_connection() as conn:
            video_comment_table_name = fatal_sql.get_video_comment_table_name(mid)
            if video_comment_table_name:
                try:
                    query_video = f"""
                        SELECT comment FROM "{video_comment_table_name}"
                        WHERE up_uid = $1 AND datetime >= $2 AND datetime < $3 AND comment IS NOT NULL AND comment != ''
                    """
                    results = await conn.fetch(
                        query_video, mid, start_time_dt, end_time_dt_query_upper_bound
                    )
                    all_texts.extend([row["comment"] for row in results])
                    logger.info(
                        f"Fetched {len(results)} comments from {video_comment_table_name} for mid {mid} word cloud."
                    )
                except asyncpg.exceptions.UndefinedTableError:
                    logger.warning(
                        f"Table {video_comment_table_name} not found for mid {mid}. Skipping video comments."
                    )
                except Exception as e_vc_wc:
                    logger.error(
                        f"Error fetching video comments for mid {mid} from {video_comment_table_name}: {e_vc_wc}"
                    )
            else:
                logger.warning(
                    f"Could not determine video comment table name for mid {mid}. Skipping video comments."
                )

            dynamics_comment_table_name = fatal_sql.get_dynamics_comment_table_name(mid)
            if dynamics_comment_table_name:
                try:
                    query_dynamics = f"""
                        SELECT comment FROM "{dynamics_comment_table_name}"
                        WHERE up_uid = $1 AND datetime >= $2 AND datetime < $3 AND comment IS NOT NULL AND comment != ''
                    """
                    results = await conn.fetch(
                        query_dynamics,
                        mid,
                        start_time_dt,
                        end_time_dt_query_upper_bound,
                    )
                    all_texts.extend([row["comment"] for row in results])
                    logger.info(
                        f"Fetched {len(results)} comments from {dynamics_comment_table_name} for mid {mid} word cloud."
                    )
                except asyncpg.exceptions.UndefinedTableError:
                    logger.warning(
                        f"Table {dynamics_comment_table_name} not found for mid {mid}. Skipping dynamics comments."
                    )
                except Exception as e_dc_wc:
                    logger.error(
                        f"Error fetching dynamics comments for mid {mid} from {dynamics_comment_table_name}: {e_dc_wc}"
                    )
            else:
                logger.warning(
                    f"Could not determine dynamics comment table name for mid {mid}. Skipping dynamics comments."
                )

            tieba_table_name = fatal_sql.get_tieba_whole_table_name(
                mid
            )  # This should be the actual table name

            # Get tieba name from vups.json using char_zh
            from utils.utils import get_vup_tieba_name_by_short_name
            tieba_fname_for_query = get_vup_tieba_name_by_short_name(char_zh)

            if tieba_table_name and tieba_fname_for_query:
                try:
                    query_tieba = f"""
                        SELECT text FROM "{tieba_table_name}"
                        WHERE fname = $1 AND create_time >= $2 AND create_time < $3 AND text IS NOT NULL AND text != ''
                    """
                    results = await conn.fetch(
                        query_tieba,
                        tieba_fname_for_query,
                        start_time_dt,
                        end_time_dt_query_upper_bound,
                    )
                    all_texts.extend([row["text"] for row in results])
                    logger.info(
                        f"Fetched {len(results)} texts from tieba '{tieba_fname_for_query}' (table: {tieba_table_name}) for word cloud."
                    )
                except asyncpg.exceptions.UndefinedTableError:
                    logger.warning(
                        f"Table {tieba_table_name} not found for tieba_fname '{tieba_fname_for_query}'. Skipping tieba content."
                    )
                except Exception as e_tb_wc:
                    logger.error(
                        f"Error fetching tieba content for tieba_fname '{tieba_fname_for_query}' from {tieba_table_name}: {e_tb_wc}"
                    )
            else:
                logger.warning(
                    f"Could not determine tieba table name ({tieba_table_name}) or fname ({tieba_fname_for_query}) for char_zh {char_zh}. Skipping tieba content."
                )

    except (psycopg2.Error, asyncpg.PostgresError) as e:
        logger.error(
            f"Database connection or general error in query_comment_wordcloud for mid {mid}: {e}"
        )
        return None
    except Exception as e_main:
        logger.error(
            f"Unexpected error in query_comment_wordcloud for mid {mid}: {e_main}"
        )
        return None

    if not all_texts:
        logger.info(
            f"No comments or texts found for mid {mid} in the time range [{start_time_str} - {end_time_str}] for word cloud."
        )
        return None

    processed_texts = [
        re.sub(r"\[.*?\]", "", text) for text in all_texts if text and text.strip()
    ]
    if not processed_texts:
        logger.info(
            f"No valid text content after processing for mid {mid} word cloud in range [{start_time_str} - {end_time_str}]."
        )
        return None

    try:
        # Ensure output directory exists
        os.makedirs(os.path.dirname(cache_file_path), exist_ok=True)

        # Generate word cloud
        word_cloud_gen(processed_texts, cache_file_path, char_zh)

        # Verify file was created successfully
        if os.path.exists(cache_file_path):
            file_size = os.path.getsize(cache_file_path)
            logger.info(f"Word cloud generated for mid {mid} at {cache_file_path} ({file_size:,} bytes)")

            # Clean up old cache files (keep only recent ones)
            cleanup_old_wordcloud_files()

            return (relative_path, False)  # False = not from cache, newly generated
        else:
            logger.error(f"Word cloud file was not created: {cache_file_path}")
            return None

    except Exception as e_wc_gen:
        logger.error(f"Error during word cloud generation for mid {mid}: {e_wc_gen}")
        return None


def cleanup_old_wordcloud_files(max_files: int = 100):
    """Clean up old word cloud files to prevent disk space issues"""
    try:
        wordcloud_dir = f"{PROJECT_ROOT}/frontend/public/wordcloud"
        if not os.path.exists(wordcloud_dir):
            return

        # Get all PNG files with their modification times
        files = []
        for filename in os.listdir(wordcloud_dir):
            if filename.endswith('.png') and not filename.startswith('test'):
                filepath = os.path.join(wordcloud_dir, filename)
                if os.path.isfile(filepath):
                    mtime = os.path.getmtime(filepath)
                    files.append((filepath, mtime))

        # Sort by modification time (newest first)
        files.sort(key=lambda x: x[1], reverse=True)

        # Remove old files if we have too many
        if len(files) > max_files:
            files_to_remove = files[max_files:]
            for filepath, _ in files_to_remove:
                try:
                    os.remove(filepath)
                    logger.info(f"Cleaned up old word cloud file: {os.path.basename(filepath)}")
                except OSError:
                    pass

    except Exception as e:
        logger.warning(f"Error during word cloud cleanup: {e}")
