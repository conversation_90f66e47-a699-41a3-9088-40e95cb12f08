#!/usr/bin/env python3
"""
Test script for the /comment/wordcloud API endpoint via HTTP requests.

This script tests the actual API endpoint to ensure it works correctly
after the fixes have been applied.
"""

import requests
import time
import json
from datetime import datetime


def test_wordcloud_api():
    """Test the word cloud API endpoint"""
    
    print("🌐 TESTING /comment/wordcloud API ENDPOINT")
    print("=" * 60)
    
    base_url = "http://localhost:9022"
    endpoint = "/comment/wordcloud"
    
    # Test parameters
    test_cases = [
        {
            "name": "JSON Response (Default)",
            "params": {
                "vtuber": "星瞳",
                "s": "2024-01-01", 
                "e": "2024-12-31"
            },
            "expected_format": "json"
        },
        {
            "name": "Direct Image Response",
            "params": {
                "vtuber": "星瞳",
                "s": "2024-01-01",
                "e": "2024-12-31",
                "format": "image"
            },
            "expected_format": "image"
        },
        {
            "name": "Cached Response Test",
            "params": {
                "vtuber": "星瞳",
                "s": "2024-06-01",
                "e": "2024-12-31"
            },
            "expected_format": "json"
        }
    ]
    
    print(f"Base URL: {base_url}")
    print(f"Endpoint: {endpoint}")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i}: {test_case['name']} ---")
        
        url = f"{base_url}{endpoint}"
        params = test_case["params"]
        expected_format = test_case["expected_format"]
        
        print(f"URL: {url}")
        print(f"Parameters: {params}")
        
        try:
            # Make request
            start_time = time.perf_counter()
            response = requests.get(url, params=params, timeout=30)
            end_time = time.perf_counter()
            
            response_time = end_time - start_time
            
            print(f"Status Code: {response.status_code}")
            print(f"Response Time: {response_time:.3f}s")
            print(f"Content Type: {response.headers.get('content-type', 'Unknown')}")
            
            if response.status_code == 200:
                if expected_format == "json":
                    # Test JSON response
                    try:
                        data = response.json()
                        print(f"✅ JSON Response received")
                        print(f"   Code: {data.get('code', 'N/A')}")
                        print(f"   Message: {data.get('message', 'N/A')}")
                        
                        if 'data' in data and 'result' in data['data']:
                            result = data['data']['result']
                            print(f"   Image Path: {result.get('image_path', 'N/A')}")
                            print(f"   Image URL: {result.get('image_url', 'N/A')}")
                            print(f"   Generated At: {result.get('generated_at', 'N/A')}")
                        
                    except json.JSONDecodeError:
                        print(f"❌ Invalid JSON response")
                        print(f"Response content: {response.text[:200]}...")
                        
                elif expected_format == "image":
                    # Test image response
                    content_type = response.headers.get('content-type', '')
                    if 'image' in content_type:
                        print(f"✅ Image response received")
                        print(f"   Content Length: {len(response.content):,} bytes")
                        
                        # Save test image
                        test_image_path = f"frontend/public/wordcloud/test_api_response_{i}.png"
                        with open(test_image_path, 'wb') as f:
                            f.write(response.content)
                        print(f"   Saved to: {test_image_path}")
                    else:
                        print(f"❌ Expected image response, got: {content_type}")
                        
            else:
                print(f"❌ Request failed")
                try:
                    error_data = response.json()
                    print(f"   Error: {error_data}")
                except:
                    print(f"   Error: {response.text}")
                    
        except requests.exceptions.ConnectionError:
            print(f"❌ Connection failed - Is the server running on {base_url}?")
        except requests.exceptions.Timeout:
            print(f"❌ Request timed out")
        except Exception as e:
            print(f"❌ Unexpected error: {e}")


def test_static_file_access():
    """Test static file access for word cloud images"""
    
    print(f"\n🗂️  TESTING STATIC FILE ACCESS")
    print("=" * 40)
    
    base_url = "http://localhost:9022"
    
    # Test existing word cloud file
    test_files = [
        "/wordcloud/星瞳.png",  # Existing file
        "/wordcloud/test/simple_test.png",  # Test file we created
    ]
    
    for file_path in test_files:
        print(f"\nTesting: {file_path}")
        url = f"{base_url}{file_path}"
        
        try:
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if 'image' in content_type:
                    print(f"   ✅ Static file accessible")
                    print(f"   Content Type: {content_type}")
                    print(f"   File Size: {len(response.content):,} bytes")
                else:
                    print(f"   ⚠️  File accessible but wrong content type: {content_type}")
            else:
                print(f"   ❌ File not accessible (Status: {response.status_code})")
                
        except requests.exceptions.ConnectionError:
            print(f"   ❌ Connection failed")
        except Exception as e:
            print(f"   ❌ Error: {e}")


def test_cache_performance():
    """Test caching performance"""
    
    print(f"\n⚡ TESTING CACHE PERFORMANCE")
    print("=" * 40)
    
    base_url = "http://localhost:9022"
    endpoint = "/comment/wordcloud"
    
    params = {
        "vtuber": "星瞳",
        "s": "2024-08-01",
        "e": "2024-08-31"
    }
    
    url = f"{base_url}{endpoint}"
    
    try:
        # First request (cache miss)
        print(f"First request (cache miss)...")
        start_time = time.perf_counter()
        response1 = requests.get(url, params=params, timeout=30)
        first_time = time.perf_counter() - start_time
        
        if response1.status_code == 200:
            print(f"   ✅ First request successful: {first_time:.3f}s")
            
            # Second request (cache hit)
            print(f"Second request (cache hit)...")
            start_time = time.perf_counter()
            response2 = requests.get(url, params=params, timeout=30)
            second_time = time.perf_counter() - start_time
            
            if response2.status_code == 200:
                print(f"   ✅ Second request successful: {second_time:.3f}s")
                
                if second_time < first_time:
                    speedup = first_time / second_time
                    print(f"   🚀 Cache speedup: {speedup:.1f}x faster")
                else:
                    print(f"   ⚠️  No significant speedup detected")
            else:
                print(f"   ❌ Second request failed")
        else:
            print(f"   ❌ First request failed")
            
    except Exception as e:
        print(f"   ❌ Cache test error: {e}")


def main():
    """Run all API tests"""
    
    print("🧪 WORD CLOUD API ENDPOINT TESTS")
    print("=" * 80)
    
    print(f"\nStarting tests at: {datetime.now().isoformat()}")
    
    # Test main API functionality
    test_wordcloud_api()
    
    # Test static file access
    test_static_file_access()
    
    # Test cache performance
    test_cache_performance()
    
    print(f"\n" + "=" * 80)
    print("✅ API TESTS COMPLETED")
    print("=" * 80)
    
    print(f"\nTest Summary:")
    print(f"• Word cloud API endpoint functionality tested")
    print(f"• JSON and image response formats tested")
    print(f"• Static file serving tested")
    print(f"• Cache performance tested")
    print(f"\nIf all tests passed, the /comment/wordcloud endpoint is working correctly!")


if __name__ == "__main__":
    main()
