#!/usr/bin/env python3
"""
Simple test for word cloud generation functionality.
Tests the core word cloud generation without full database dependencies.
"""

import os
import sys
import platform
from PIL import Image
import numpy as np

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_dependencies():
    """Test if required dependencies are available"""
    print("🔍 TESTING DEPENDENCIES")
    print("=" * 40)
    
    dependencies = {
        "jieba": "Chinese text segmentation",
        "wordcloud": "Word cloud generation",
        "PIL": "Image processing",
        "numpy": "Numerical operations"
    }
    
    missing_deps = []
    
    for dep, description in dependencies.items():
        try:
            if dep == "PIL":
                import PIL
                print(f"   ✅ {dep}: {description}")
            else:
                __import__(dep)
                print(f"   ✅ {dep}: {description}")
        except ImportError:
            print(f"   ❌ {dep}: {description} - MISSING")
            missing_deps.append(dep)
    
    if missing_deps:
        print(f"\n❌ Missing dependencies: {', '.join(missing_deps)}")
        print("Please install missing dependencies:")
        for dep in missing_deps:
            if dep == "PIL":
                print(f"   pip install Pillow")
            else:
                print(f"   pip install {dep}")
        return False
    else:
        print(f"\n✅ All dependencies available!")
        return True


def test_font_detection():
    """Test font detection for different operating systems"""
    print(f"\n🔤 TESTING FONT DETECTION")
    print("=" * 40)
    
    system = platform.system()
    print(f"   Operating System: {system}")
    
    # Test custom font path
    custom_font = "backend/utils/fonts/msyh.ttf"
    print(f"   Custom font path: {custom_font}")
    if os.path.exists(custom_font):
        print(f"   ✅ Custom font exists")
        return custom_font
    else:
        print(f"   ❌ Custom font not found")
    
    # System-specific font paths
    if system == "Windows":
        fonts = [
            "C:/Windows/Fonts/msyh.ttc",  # Microsoft YaHei
            "C:/Windows/Fonts/simhei.ttf",  # SimHei
            "C:/Windows/Fonts/simsun.ttc",  # SimSun
            "C:/Windows/Fonts/arial.ttf",  # Fallback
        ]
    elif system == "Darwin":  # macOS
        fonts = [
            "/System/Library/Fonts/PingFang.ttc",
            "/System/Library/Fonts/STHeiti Light.ttc",
            "/System/Library/Fonts/Arial.ttf",
        ]
    else:  # Linux
        fonts = [
            "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
            "/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf",
            "/usr/share/fonts/TTF/arial.ttf",
        ]
    
    print(f"   Checking system fonts...")
    for font_path in fonts:
        if os.path.exists(font_path):
            print(f"   ✅ Found: {font_path}")
            return font_path
        else:
            print(f"   ❌ Not found: {font_path}")
    
    print(f"   ⚠️  No suitable font found, will use default")
    return None


def test_mask_image():
    """Test mask image loading"""
    print(f"\n🖼️  TESTING MASK IMAGE")
    print("=" * 40)
    
    mask_path = "data/images/vtubers/星瞳.jpg"
    print(f"   Mask image path: {mask_path}")
    
    if os.path.exists(mask_path):
        try:
            mask_image = np.array(Image.open(mask_path))
            print(f"   ✅ Mask image loaded successfully")
            print(f"   📊 Image shape: {mask_image.shape}")
            return mask_image
        except Exception as e:
            print(f"   ❌ Error loading mask image: {e}")
            return None
    else:
        print(f"   ❌ Mask image not found")
        return None


def test_simple_wordcloud():
    """Test simple word cloud generation"""
    print(f"\n🎨 TESTING WORD CLOUD GENERATION")
    print("=" * 40)
    
    try:
        from wordcloud import WordCloud
        import jieba
        
        # Sample Chinese text
        sample_texts = [
            "星瞳真的太可爱了",
            "今天的直播很有趣",
            "感谢星瞳的精彩表演",
            "期待下次直播",
            "星瞳加油",
            "很喜欢这个游戏",
            "笑死我了哈哈哈",
            "星瞳的声音很好听",
            "这个梗太好笑了",
            "支持星瞳"
        ]
        
        print(f"   Sample texts: {len(sample_texts)} items")
        
        # Process text
        context = " ".join(sample_texts)
        words = jieba.lcut(context)
        newtxt = " ".join(words)
        
        print(f"   Processed text length: {len(newtxt)} characters")
        
        # Get font and mask
        font_path = test_font_detection()
        mask_image = test_mask_image()
        
        # Create output directory
        output_dir = "frontend/public/wordcloud/test"
        os.makedirs(output_dir, exist_ok=True)
        output_path = f"{output_dir}/simple_test.png"
        
        # Word cloud parameters
        wc_params = {
            "width": 800,
            "height": 800,
            "background_color": "white",
            "max_words": 500,
            "min_font_size": 2,
            "contour_width": 0.5,
            "contour_color": "yellow",
            "relative_scaling": 0.5,
            "random_state": 42,
        }
        
        if font_path:
            wc_params["font_path"] = font_path
            print(f"   Using font: {font_path}")
        else:
            print(f"   Using default font")
            
        if mask_image is not None:
            wc_params["mask"] = mask_image
            print(f"   Using mask image")
        else:
            print(f"   Using default shape")
        
        # Generate word cloud
        print(f"   Generating word cloud...")
        wordcloud = WordCloud(**wc_params).generate(newtxt)
        wordcloud.to_file(output_path)
        
        if os.path.exists(output_path):
            file_size = os.path.getsize(output_path)
            print(f"   ✅ Word cloud generated successfully!")
            print(f"   📁 Output: {output_path}")
            print(f"   📊 File size: {file_size:,} bytes")
            return True
        else:
            print(f"   ❌ Word cloud file not created")
            return False
            
    except ImportError as e:
        print(f"   ❌ Missing dependency: {e}")
        return False
    except Exception as e:
        print(f"   ❌ Error generating word cloud: {e}")
        return False


def test_directory_structure():
    """Test required directory structure"""
    print(f"\n📁 TESTING DIRECTORY STRUCTURE")
    print("=" * 40)
    
    required_dirs = [
        "frontend/public/wordcloud",
        "data/images/vtubers",
        "backend/utils/fonts",
        "utils"
    ]
    
    all_exist = True
    
    for dir_path in required_dirs:
        if os.path.exists(dir_path):
            print(f"   ✅ {dir_path}")
        else:
            print(f"   ❌ {dir_path} - MISSING")
            all_exist = False
            
            # Try to create missing directories
            try:
                os.makedirs(dir_path, exist_ok=True)
                print(f"      ✅ Created directory: {dir_path}")
            except Exception as e:
                print(f"      ❌ Failed to create directory: {e}")
    
    return all_exist


def main():
    """Run all tests"""
    print("🧪 SIMPLE WORD CLOUD FUNCTIONALITY TEST")
    print("=" * 80)
    
    # Test directory structure
    test_directory_structure()
    
    # Test dependencies
    if not test_dependencies():
        print(f"\n❌ Cannot proceed without required dependencies")
        return
    
    # Test word cloud generation
    success = test_simple_wordcloud()
    
    print(f"\n" + "=" * 80)
    if success:
        print("✅ WORD CLOUD TEST COMPLETED SUCCESSFULLY")
        print("=" * 80)
        print(f"\nThe word cloud generation functionality is working!")
        print(f"You can now test the API endpoint:")
        print(f"GET /comment/wordcloud/?vtuber=星瞳&s=2024-01-01&e=2024-12-31")
    else:
        print("❌ WORD CLOUD TEST FAILED")
        print("=" * 80)
        print(f"\nPlease check the error messages above and fix the issues.")


if __name__ == "__main__":
    main()
