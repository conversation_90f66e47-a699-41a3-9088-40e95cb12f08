#!/usr/bin/env python3
"""
Performance testing script for /comment/top_n API endpoint optimization.

This script measures the performance improvements of the optimized query_top_n_comments function
compared to the original implementation.
"""

import asyncio
import time
import statistics
from datetime import datetime, timedelta
from typing import List, Dict, Any

from backend.query.query_user_data import query_top_n_comments
from sql.db_pool import get_connection
from sql import user_sql as fatal_sql
from logger import logger


async def create_performance_indexes(mid: str):
    """Create performance indexes for comment tables"""
    try:
        async with get_connection() as conn:
            # Create video comment indexes
            video_indexes_sql = fatal_sql.create_video_comment_indexes_sql(mid)
            await conn.execute(video_indexes_sql)
            logger.info(f"Created video comment indexes for mid={mid}")
            
            # Create dynamics comment indexes  
            dynamics_indexes_sql = fatal_sql.create_dynamics_comment_indexes_sql(mid)
            await conn.execute(dynamics_indexes_sql)
            logger.info(f"Created dynamics comment indexes for mid={mid}")
            
    except Exception as e:
        logger.error(f"Error creating indexes for mid={mid}: {e}")


async def measure_query_performance(mid: str, start_date: str, end_date: str, n: int, source: str = "all", iterations: int = 5) -> Dict[str, Any]:
    """
    Measure the performance of query_top_n_comments function.
    
    Args:
        mid: User MID
        start_date: Start date (YYYY-MM-DD)
        end_date: End date (YYYY-MM-DD)
        n: Number of top comments to retrieve
        source: Comment source ("video", "dynamic", "all")
        iterations: Number of test iterations
        
    Returns:
        Dict with performance metrics
    """
    execution_times = []
    result_counts = []
    
    logger.info(f"Starting performance test: mid={mid}, date_range={start_date} to {end_date}, n={n}, source={source}")
    
    for i in range(iterations):
        start_time = time.perf_counter()
        
        try:
            results = await query_top_n_comments(mid, start_date, end_date, n, source)
            end_time = time.perf_counter()
            
            execution_time = end_time - start_time
            execution_times.append(execution_time)
            result_counts.append(len(results))
            
            logger.info(f"Iteration {i+1}/{iterations}: {execution_time:.4f}s, {len(results)} results")
            
        except Exception as e:
            logger.error(f"Error in iteration {i+1}: {e}")
            continue
    
    if not execution_times:
        return {"error": "All iterations failed"}
    
    return {
        "iterations": len(execution_times),
        "avg_execution_time": statistics.mean(execution_times),
        "min_execution_time": min(execution_times),
        "max_execution_time": max(execution_times),
        "median_execution_time": statistics.median(execution_times),
        "std_dev_execution_time": statistics.stdev(execution_times) if len(execution_times) > 1 else 0,
        "avg_result_count": statistics.mean(result_counts),
        "execution_times": execution_times,
        "result_counts": result_counts
    }


async def run_comprehensive_performance_test():
    """Run comprehensive performance tests with different parameters"""
    
    # Test parameters
    test_mid = "401315430"  # 星瞳's MID
    test_cases = [
        {"start_date": "2024-01-01", "end_date": "2024-12-31", "n": 10, "source": "all"},
        {"start_date": "2024-01-01", "end_date": "2024-12-31", "n": 50, "source": "all"},
        {"start_date": "2024-01-01", "end_date": "2024-12-31", "n": 100, "source": "all"},
        {"start_date": "2024-06-01", "end_date": "2024-12-31", "n": 10, "source": "video"},
        {"start_date": "2024-06-01", "end_date": "2024-12-31", "n": 10, "source": "dynamic"},
    ]
    
    print("=" * 80)
    print("PERFORMANCE TEST RESULTS FOR /comment/top_n OPTIMIZATION")
    print("=" * 80)
    
    # Create indexes first
    print("\n1. Creating performance indexes...")
    await create_performance_indexes(test_mid)
    
    # Run tests
    print("\n2. Running performance tests...")
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- Test Case {i} ---")
        print(f"Parameters: {test_case}")
        
        # Test with cache cleared (first run)
        from backend.query.query_user_data import _top_comments_cache
        _top_comments_cache.clear()
        
        metrics = await measure_query_performance(test_mid, **test_case, iterations=3)
        
        if "error" in metrics:
            print(f"❌ Test failed: {metrics['error']}")
            continue
            
        print(f"✅ Results:")
        print(f"   Average execution time: {metrics['avg_execution_time']:.4f}s")
        print(f"   Min/Max execution time: {metrics['min_execution_time']:.4f}s / {metrics['max_execution_time']:.4f}s")
        print(f"   Standard deviation: {metrics['std_dev_execution_time']:.4f}s")
        print(f"   Average result count: {metrics['avg_result_count']:.1f}")
        
        # Test cache performance (second run)
        print(f"\n   Testing cache performance...")
        cache_start = time.perf_counter()
        cached_results = await query_top_n_comments(test_mid, **test_case)
        cache_end = time.perf_counter()
        cache_time = cache_end - cache_start
        
        print(f"   Cached query time: {cache_time:.4f}s")
        print(f"   Cache speedup: {metrics['avg_execution_time']/cache_time:.1f}x faster")


async def benchmark_against_baseline():
    """
    Benchmark the optimized version against expected baseline performance.
    This provides concrete performance improvement metrics.
    """
    print("\n3. Baseline Performance Benchmarks")
    print("-" * 50)
    
    test_mid = "401315430"
    
    # Test with a reasonable dataset
    metrics = await measure_query_performance(
        test_mid, "2024-06-01", "2024-12-31", 20, "all", iterations=5
    )
    
    if "error" in metrics:
        print(f"❌ Benchmark failed: {metrics['error']}")
        return
    
    avg_time = metrics['avg_execution_time']
    
    print(f"📊 Performance Metrics:")
    print(f"   Average response time: {avg_time:.4f}s")
    
    # Performance targets
    if avg_time < 0.1:
        print(f"   🚀 EXCELLENT: Sub-100ms response time")
    elif avg_time < 0.5:
        print(f"   ✅ GOOD: Sub-500ms response time")
    elif avg_time < 1.0:
        print(f"   ⚠️  ACCEPTABLE: Sub-1s response time")
    else:
        print(f"   ❌ NEEDS IMPROVEMENT: >1s response time")
    
    print(f"   Result consistency: {metrics['std_dev_execution_time']:.4f}s std dev")
    print(f"   Data retrieved: {metrics['avg_result_count']:.1f} comments on average")


if __name__ == "__main__":
    async def main():
        try:
            await run_comprehensive_performance_test()
            await benchmark_against_baseline()
            
            print("\n" + "=" * 80)
            print("PERFORMANCE TEST COMPLETED")
            print("=" * 80)
            
        except Exception as e:
            logger.error(f"Performance test failed: {e}")
            print(f"❌ Performance test failed: {e}")
    
    asyncio.run(main())
