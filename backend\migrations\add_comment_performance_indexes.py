#!/usr/bin/env python3
"""
Database migration script to add performance indexes for comment tables.

This script creates indexes on comment tables to optimize the /comment/top_n endpoint performance.
It should be run once to apply the indexes to existing comment tables.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from sql.db_pool import get_connection
from sql import user_sql as fatal_sql
from logger import logger


async def get_all_comment_table_names():
    """Get all existing comment table names from the database"""
    try:
        async with get_connection() as conn:
            # Query for video comment tables
            video_tables_query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'video_comment_%'
            """
            video_tables = await conn.fetch(video_tables_query)
            
            # Query for dynamics comment tables
            dynamics_tables_query = """
                SELECT table_name 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name LIKE 'dynamics_comment_%'
            """
            dynamics_tables = await conn.fetch(dynamics_tables_query)
            
            video_table_names = [row['table_name'] for row in video_tables]
            dynamics_table_names = [row['table_name'] for row in dynamics_tables]
            
            return video_table_names, dynamics_table_names
            
    except Exception as e:
        logger.error(f"Error getting comment table names: {e}")
        return [], []


async def create_indexes_for_table(table_name: str, table_type: str):
    """Create performance indexes for a specific table"""
    try:
        async with get_connection() as conn:
            if table_type == "video":
                # Create video comment indexes
                indexes_sql = f"""
                    CREATE INDEX IF NOT EXISTS idx_{table_name}_datetime ON {table_name} (datetime);
                    CREATE INDEX IF NOT EXISTS idx_{table_name}_like_num ON {table_name} (like_num DESC);
                    CREATE INDEX IF NOT EXISTS idx_{table_name}_heat ON {table_name} (heat DESC);
                    CREATE INDEX IF NOT EXISTS idx_{table_name}_datetime_heat ON {table_name} (datetime, heat DESC);
                    CREATE INDEX IF NOT EXISTS idx_{table_name}_bvid ON {table_name} (bvid);
                """
            elif table_type == "dynamics":
                # Create dynamics comment indexes
                indexes_sql = f"""
                    CREATE INDEX IF NOT EXISTS idx_{table_name}_datetime ON {table_name} (datetime);
                    CREATE INDEX IF NOT EXISTS idx_{table_name}_like_num ON {table_name} (like_num DESC);
                    CREATE INDEX IF NOT EXISTS idx_{table_name}_heat ON {table_name} (heat DESC);
                    CREATE INDEX IF NOT EXISTS idx_{table_name}_datetime_heat ON {table_name} (datetime, heat DESC);
                    CREATE INDEX IF NOT EXISTS idx_{table_name}_oid ON {table_name} (oid);
                """
            else:
                logger.error(f"Unknown table type: {table_type}")
                return False
            
            # Execute the index creation
            await conn.execute(indexes_sql)
            logger.info(f"✅ Created indexes for {table_type} table: {table_name}")
            return True
            
    except Exception as e:
        logger.error(f"❌ Error creating indexes for table {table_name}: {e}")
        return False


async def check_existing_indexes(table_name: str):
    """Check what indexes already exist for a table"""
    try:
        async with get_connection() as conn:
            query = """
                SELECT indexname, indexdef 
                FROM pg_indexes 
                WHERE tablename = $1 
                AND schemaname = 'public'
                ORDER BY indexname
            """
            indexes = await conn.fetch(query, table_name)
            return [(row['indexname'], row['indexdef']) for row in indexes]
            
    except Exception as e:
        logger.error(f"Error checking indexes for table {table_name}: {e}")
        return []


async def run_migration():
    """Run the complete migration to add performance indexes"""
    
    print("=" * 80)
    print("COMMENT PERFORMANCE INDEXES MIGRATION")
    print("=" * 80)
    
    print("\n1. Discovering existing comment tables...")
    video_tables, dynamics_tables = await get_all_comment_table_names()
    
    print(f"   Found {len(video_tables)} video comment tables")
    print(f"   Found {len(dynamics_tables)} dynamics comment tables")
    
    if not video_tables and not dynamics_tables:
        print("   ⚠️  No comment tables found. Migration not needed.")
        return
    
    print("\n2. Creating indexes for video comment tables...")
    video_success = 0
    for table_name in video_tables:
        print(f"   Processing: {table_name}")
        
        # Check existing indexes
        existing_indexes = await check_existing_indexes(table_name)
        performance_indexes = [idx for idx, _ in existing_indexes if 'datetime' in idx or 'heat' in idx or 'like_num' in idx]
        
        if performance_indexes:
            print(f"     ⏭️  Skipping (indexes already exist): {len(performance_indexes)} performance indexes found")
            video_success += 1
            continue
        
        success = await create_indexes_for_table(table_name, "video")
        if success:
            video_success += 1
    
    print(f"   ✅ Video tables processed: {video_success}/{len(video_tables)} successful")
    
    print("\n3. Creating indexes for dynamics comment tables...")
    dynamics_success = 0
    for table_name in dynamics_tables:
        print(f"   Processing: {table_name}")
        
        # Check existing indexes
        existing_indexes = await check_existing_indexes(table_name)
        performance_indexes = [idx for idx, _ in existing_indexes if 'datetime' in idx or 'heat' in idx or 'like_num' in idx]
        
        if performance_indexes:
            print(f"     ⏭️  Skipping (indexes already exist): {len(performance_indexes)} performance indexes found")
            dynamics_success += 1
            continue
        
        success = await create_indexes_for_table(table_name, "dynamics")
        if success:
            dynamics_success += 1
    
    print(f"   ✅ Dynamics tables processed: {dynamics_success}/{len(dynamics_tables)} successful")
    
    print("\n4. Migration Summary:")
    total_tables = len(video_tables) + len(dynamics_tables)
    total_success = video_success + dynamics_success
    
    print(f"   Total tables processed: {total_success}/{total_tables}")
    
    if total_success == total_tables:
        print("   🎉 Migration completed successfully!")
        print("   📈 The /comment/top_n endpoint should now have improved performance.")
    else:
        print(f"   ⚠️  Migration partially completed. {total_tables - total_success} tables had issues.")
    
    print("\n" + "=" * 80)


async def verify_indexes():
    """Verify that indexes were created correctly"""
    print("\n5. Verifying index creation...")
    
    video_tables, dynamics_tables = await get_all_comment_table_names()
    
    for table_name in video_tables[:3]:  # Check first 3 tables as sample
        indexes = await check_existing_indexes(table_name)
        performance_indexes = [idx for idx, _ in indexes if any(perf in idx for perf in ['datetime', 'heat', 'like_num'])]
        print(f"   {table_name}: {len(performance_indexes)} performance indexes")
    
    for table_name in dynamics_tables[:3]:  # Check first 3 tables as sample
        indexes = await check_existing_indexes(table_name)
        performance_indexes = [idx for idx, _ in indexes if any(perf in idx for perf in ['datetime', 'heat', 'like_num'])]
        print(f"   {table_name}: {len(performance_indexes)} performance indexes")


if __name__ == "__main__":
    async def main():
        try:
            await run_migration()
            await verify_indexes()
            
        except Exception as e:
            logger.error(f"Migration failed: {e}")
            print(f"❌ Migration failed: {e}")
            sys.exit(1)
    
    asyncio.run(main())
